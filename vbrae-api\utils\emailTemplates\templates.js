export function newOfferMail(offer) {
    return {
      data: offer,
      subject: `🎉 New Deal in ${offer.region}: ${offer.name}`,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 24px; background-color: #f4f6f8; color: #333;">
          <h2 style="color: #007BFF;">🎮 New Gaming Deal Just Dropped!</h2>
          <p style="font-size: 16px;">Hello!</p>
          <p style="font-size: 16px;">
            We're excited to share a new offer: <strong>${offer.name}</strong> is now available in your region: <strong>${offer.region}</strong>.
          </p>
          <p style="font-size: 16px;">Don't miss this exciting opportunity to grab it at a great price!</p>
          <a href="${process.env.FRONTEND_URL + '/details/' + offer._id}" style="display:inline-block; margin-top:20px; padding:12px 24px; background-color:#007BFF; color:#fff; text-decoration:none; border-radius:6px;">
            View Offer
          </a>
        </div>
      `,
    };
  }  
  
  export function flashSaleMail() {
    return {
      subject: `⚡ Flash Sale is Live — Grab Deals While They Last!`,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 24px; background-color: #fff3e6; color: #333;">
          <h2 style="color: #d9534f;">⚡ Flash Sale is Here!</h2>
          <p style="font-size: 16px;">Act fast! Our exclusive flash sale on select products is now live for a limited time only.</p>
          <a href="https://walrus-app-yy5iv.ondigitalocean.app/" style="display:inline-block; margin-top:20px; padding:12px 24px; background-color:#d9534f; color:#fff; text-decoration:none; border-radius:6px;">
            Explore Deals
          </a>
        </div>
      `,
    };
  }
  
  
  export function boosterActiveMail() {
    return {
      subject: `🚀 Boosters Activated – Increase Your Offer's Visibility`,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 24px; background-color: #e6f7ff; color: #333;">
          <h2 style="color: #007BFF;">🚀 Boosters Are Live!</h2>
          <p style="font-size: 16px;">Boost your visibility and reach more users with our new booster feature.</p>
          <p style="font-size: 16px;">Elevate your offers and stand out in the marketplace today.</p>
          <a href="https://walrus-app-yy5iv.ondigitalocean.app/" style="display:inline-block; margin-top:20px; padding:12px 24px; background-color:#007BFF; color:#fff; text-decoration:none; border-radius:6px;">
            Boost Now
          </a>
        </div>
      `,
    };
  }
  
  
  export function inactiveUserMail() {
    return {
      subject: `👋 We've Missed You – Here's What's New!`,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 24px; background-color: #f0f4f8; color: #333;">
          <h2 style="color: #6c757d;">👋 It's Been a While...</h2>
          <p style="font-size: 16px;">We noticed you haven't been active lately, and we've introduced some amazing new offers and features!</p>
          <p style="font-size: 16px;">Come take a look — we think you'll love the updates.</p>
          <a href="https://walrus-app-yy5iv.ondigitalocean.app/" style="display:inline-block; margin-top:20px; padding:12px 24px; background-color:#17a2b8; color:#fff; text-decoration:none; border-radius:6px;">
            See What's New
          </a>
        </div>
      `,
    };
  }
  
  
  export function newBlogMail(blog) {
    return {
      data: blog,
      subject: `📝 New Blog Post: ${blog.title}`,
      html: `
        <div style="font-family: Arial, sans-serif; padding: 24px; background-color: #fcfcfc; color: #333;">
          <h2 style="color: #343a40;">📝 ${blog.title}</h2>
          <p style="font-size: 16px; margin-bottom: 16px;">${blog.shortSummary || blog.summary}</p>
          <p style="font-size: 14px; color: #6c757d;">Category: <strong>${blog.category?.categoryName || "General"}</strong> | Tags: ${blog.tags.join(', ')}</p>
          <a href="${process.env.FRONTEND_URL + '/blog-details/' + blog._id}" style="display:inline-block; margin-top:20px; padding:12px 24px; background-color:#28a745; color:#fff; text-decoration:none; border-radius:6px;">
            Read More
          </a>
        </div>
      `,
    };
  }
  
  export function orderCompletionMail(order, user) {
    return {
      subject: `Order Completed`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 24px; background-color: #ffffff; color: #333;">
          <div style="margin-bottom: 24px;">
            <h2 style="font-size: 16px; font-weight: normal; margin: 0;">Dear, ${user.name}</h2>
          </div>
          
          <div style="margin-bottom: 32px;">
            <p style="font-size: 14px; line-height: 1.6; margin: 0;">
              We Happy to inform you that the order <span style="color: #0078FF; font-weight: normal;">#${order.orderNumber}</span> have just been deliverd to your inventory.
            </p>
          </div>

          <div style="background-color: #f5f5f5; padding: 16px; border-radius: 8px; text-align: center; margin-bottom: 32px;">
            <p style="font-size: 14px; margin: 0; color: #333;">
              In case of any issues with the key, please report it to seller.
            </p>
          </div>

          <div style="margin-bottom: 32px;">
            <p style="font-size: 14px; line-height: 1.6; margin: 0;">
              Enjoy your products and if you have any questions, feel free to 
              <a href="${process.env.FRONTEND_URL}/contact" style="color: #FFA500; text-decoration: none;">contact us</a>.
            </p>
          </div>

          <div style="text-align: right; margin-bottom: 32px;">
            <p style="font-size: 14px; margin: 0 0 8px 0;">Best Regards,</p>
            <p style="font-size: 14px; margin: 0;">Vbrae Customer Support</p>
          </div>

          <div style="border-top: 1px solid #eee; padding-top: 24px;">
            <div style="font-size: 14px; color: #666; margin-bottom: 16px;">
              <p style="margin: 0 0 8px 0;">Oskarström - Hantverksgatan 12 - 31331</p>
            </div>
            <div style="font-size: 14px; color: #666;">
              <p style="margin: 0;">
                Copyright 2018-2025 
                <a href="https://walrus-app-yy5iv.ondigitalocean.app/" style="color: #0078FF; text-decoration: none;">VBRAE.COM</a>
                - All Rights Reserved. Vbrae Games,
                <a href="#" style="color: #0078FF; text-decoration: none;">Hantverksgatan 12</a>,
                Oskarstrom, Sweden
              </p>
            </div>
          </div>
        </div>
      `
    };
  }
  
  