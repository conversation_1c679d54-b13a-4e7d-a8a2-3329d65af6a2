import express from "express";
import {
    createGiveaway,
    deleteGiveaway,
    getAllGiveaways,
    getGiveawayById,
    updateGiveaway
} from "../controllers/giveAwayController.js";

const router = express.Router();
router.get('/', getAllGiveaways);
router.get('/:id', getGiveawayById);
router.post('/', createGiveaway);
router.patch('/:id', updateGiveaway);
router.delete('/:id', deleteGiveaway);

export default router;