import asyncHandler from "express-async-handler";
import NewsletterModel from "../models/newsLetterModal.js";
import Coupon from "../models/couponModel.js";
import sendgrid from "@sendgrid/mail";

export const postNewsLetter = asyncHandler(async (req, res) => {
    const {email, country} = req.body;

    const existingNewsletter = await NewsletterModel.findOne({email});

    if(existingNewsletter){
        return res.status(400).json({
            success: false,
            message: "Email Already exists"
        });
    }
    
    const newsletter = await NewsletterModel.create({
        email, country
    })

    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const len = 8;

    const randomCode = Array(len).fill(0).map(() => chars[Math.floor(Math.random() * chars.length)]).join('');

    const expirationDate = new Date();
    expirationDate.setMonth(expirationDate.getMonth() + 1);
    const coupon = await Coupon.create({
        code: randomCode,
        discountValue: 15,
        totalCoupons: 1,
        minOrderAmount: 1,
        couponUserType: "single",
        expirationDate: (() => {
            const date = new Date();
            date.setMonth(date.getMonth() + 1);
            return date;
        })(),
        applicableCategories: [
            "677e502ca4b888226ea383df",
            "6785581e1d21b77dbbb086bc",
            "678697a6bc38a6777c6f8656",
            "678698febc38a6777c6f869a",
            "6786994cbc38a6777c6f86aa",
            "678699ecbc38a6777c6f86c5",
            "67869a091acf6a353c7ace07",
            "67869a191acf6a353c7ace0a",
            "67869a271acf6a353c7ace0d",
            "67869a441acf6a353c7ace10"
        ],
        appliesToAllCategories: true,
        discountType: "percentage",
        isActive: true
    })

    const mailOptions = {
        to: email,
        from: process.env.EMAIL_FROM,
        subject: "🎉 You're Subscribed! Here's Your Coupon Code 🎁",
        html: `
      <h2>🎉 Welcome to Our Newsletter!</h2>
      <p>Thank you for subscribing. Here’s your exclusive coupon code:</p>
      <h3 style="color:#1095ED;">${coupon.code}</h3>
      <p>Use it at checkout for 15% off. Stay tuned for more updates!</p>
      <p>Best regards,<br/>Vbrae</p>
    `,
    };

    await sendgrid.send(mailOptions);

    res.status(201).json({
        success: true,
        data: newsletter,
        coupon
    });
  });