import express from "express";
import {
  createCoupon,
  getCoupon,
  getAllCoupons,
  updateCoupon,
  deleteCoupon,
  applyCoupon,
} from "../controllers/couponController.js";
import { protect, admin , seller } from "../middleware/authMiddleware.js";

const router = express.Router();
// Public routes
router.route("/").get(getAllCoupons);
router.route("/:id").get(getCoupon);

// Protected routes (admin only)
// router.use(protect, admin, seller);
router.route("/").post(createCoupon);
router.route('/apply-coupon').post(protect, applyCoupon);
router.route("/:id").patch(updateCoupon).delete(deleteCoupon);

export default router;
