class APIFeatures {
  constructor(query, queryString) {
    this.query = query;
    this.queryString = queryString;
  }
  filter() {
    const queryObj = { ...this.queryString };
    const excludedFields = ["page", "sort", "limit", "fields"];
    excludedFields.forEach((el) => delete queryObj[el]);
    // 1B) Advanced filtering
    let queryStr = JSON.stringify(queryObj);
    queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);
    this.query = this.query.find(JSON.parse(queryStr));

    return this;
  }

  // advancefilter() {
  //   const queryObj = { ...this.queryString };
  //   const excludedFields = ["page", "sort", "limit", "fields"];
  //   excludedFields.forEach((el) => delete queryObj[el]);

  //   // Advanced filtering
  //   let queryStr = JSON.stringify(queryObj);

  //   // Handle comparison operators
  //   queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, (match) => `$${match}`);

  //   // Convert the stringified query object into JSON
  //   let parsedQuery = JSON.parse(queryStr);

  //   // Enhance filtering for partial matches
  //   for (const key in parsedQuery) {
  //     if (typeof parsedQuery[key] === "string") {
  //       // Add regex-based search for strings (case-insensitive)
  //       parsedQuery[key] = { $regex: parsedQuery[key], $options: "i" };
  //     }
  //   }

  //   // Apply the query
  //   this.query = this.query.find(parsedQuery);

  //   return this;
  // }
  // utils/apiFeatures.js
  advancefilter() {
    const queryObj = { ...this.queryString };
    const excludedFields = ["page", "sort", "limit", "fields"];
    excludedFields.forEach((el) => delete queryObj[el]);

    // Check if filtering by genres (which exists in Template)
    if (queryObj.genres) {
      // Use aggregation pipeline to join Offers with Templates
      this.query = this.query.model.aggregate([
        {
          $lookup: {
            from: "templates", // Collection name in MongoDB (lowercase + plural)
            localField: "template",
            foreignField: "_id",
            as: "template",
          },
        },
        { $unwind: "$template" },
        {
          $match: {
            "template.genres": { $regex: queryObj.genres, $options: "i" },
          },
        },
      ]);
      delete queryObj.genres; // Remove genres from the query object
    } else if (queryObj.listingType) {
      this.query = this.query.model.aggregate([
        {
          $lookup: {
            from: "templates", // Collection name in MongoDB (lowercase + plural)
            localField: "template",
            foreignField: "_id",
            as: "template",
          },
        },
        { $unwind: "$template" },
        {
          $match: {
            "template.listingType": {
              $regex: queryObj.listingType,
              $options: "i",
            },
          },
        },
      ]);
      delete queryObj.listingType;
    } else {
      // Existing filtering logic for other fields
      let queryStr = JSON.stringify(queryObj);
      queryStr = queryStr.replace(
        /\b(gte|gt|lte|lt)\b/g,
        (match) => `$${match}`
      );
      this.query = this.query.find(JSON.parse(queryStr));
    }

    return this;
  }
  sort() {
    if (this.queryString.sort) {
      const sortBy = this.queryString.sort.split(",").join(" ");
      this.query = this.query.sort(sortBy);
    } else {
      this.query = this.query.sort("-createdAt");
    }

    return this;
  }

  // limitFields() {
  //   if (this.queryString.fields) {
  //     const fields = this.queryString.fields.split(",").join(" ");
  //     this.query = this.query.select(fields);
  //   } else {
  //     this.query = this.query.select("-__v");
  //   }
  //   return this;
  // }

  paginate() {
    const page = this.queryString.page * 1 || 1;
    const limit = this.queryString.limit * 1 || 10;
    const skip = (page - 1) * limit;
    this.query = this.query.skip(skip).limit(limit);

    return this;
  }

  populate(populateOptions) {
    if (populateOptions) {
      populateOptions.forEach((option) => {
        this.query = this.query.populate(option);
      });
    }
    return this;
  }

  search(key) {
    if (this.queryString.search && key) {
      const searchTerm = this.queryString.search;
      const regex = new RegExp(searchTerm, "i"); // Case-insensitive pattern matching
      this.query = this.query.find({ [key]: regex });
    }
    return this;
  }
}
export default APIFeatures;
