import express from "express";
import {getAllNotifications, patchNotification, postNotification, deleteNotification} from "../controllers/notificationController.js";
import {protect} from "../middleware/authMiddleware.js";

const router = express.Router();

router.route("/").get(protect, getAllNotifications)
                .patch(protect, patchNotification)
                .post(protect, postNotification)

router.route("/:_id").delete(protect, deleteNotification)

export default router;
