import mongoose from "mongoose";

const homepageConfigSchema = new mongoose.Schema(
  {
    topOffers: {
      enabled: {
        type: Boolean,
        default: true,
      },
      limit: {
        type: Number,
        default: 5,
        min: 1,
        max: 20,
      },
    },
    softwares: {
      enabled: {
        type: Boolean,
        default: true,
      },
      limit: {
        type: Number,
        default: 5,
        min: 1,
        max: 20,
      },
    },
    newArrivals: {
      enabled: {
        type: Boolean,
        default: true,
      },
      limit: {
        type: Number,
        default: 5,
        min: 1,
        max: 20,
      },
    },
    articles: {
      enabled: {
        type: Boolean,
        default: true,
      },
      limit: {
        type: Number,
        default: 5,
        min: 1,
        max: 20,
      },
    },
  },
  { timestamps: true }
);

const HomepageConfig = mongoose.model("HomepageConfig", homepageConfigSchema);

export default HomepageConfig;
