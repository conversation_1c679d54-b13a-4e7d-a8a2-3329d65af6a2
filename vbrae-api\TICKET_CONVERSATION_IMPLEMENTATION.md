# Ticket Conversation Implementation

## Overview
This document outlines the implementation of conversation functionality for the ticket system, allowing users and admins to communicate through messages within ticket contexts.

## Changes Made

### 1. Updated Ticket Controller (`controllers/ticketController.js`)

#### Modified Functions:
- **`getAllTickets`**: Now includes conversation messages for each ticket
- **`getTicketById`**: Now includes conversation messages for the specific ticket
- **`getUserTickets`**: Now includes conversation messages for user's tickets

#### New Functions:
- **`getTicketMessages`**: Get paginated messages for a specific ticket conversation
- **`postTicketMessage`**: Post a new message to a ticket conversation

### 2. Updated Ticket Routes (`routes/ticketRoutes.js`)

#### New Routes:
- `GET /tickets/:ticketId/messages` - Get messages for a ticket conversation
- `POST /tickets/:ticketId/messages` - Post a message to a ticket conversation

### 3. Features Implemented

#### Authorization:
- **Admin users**: Can access all ticket conversations
- **Regular users**: Can only access their own ticket conversations
- **Guest tickets**: Access controlled by email verification

#### Message Management:
- Paginated message retrieval (default 50 messages per page)
- Real-time conversation updates
- Automatic ticket status updates when messages are posted
- Unread message counting for both client and admin

#### Data Population:
- Full user information for message senders
- Complete conversation details including participants
- Ticket metadata (number, subject, status)

## API Endpoints

### Get All Tickets with Conversations
```
GET /tickets
Authorization: Bearer <token>
```

**Response includes:**
- Ticket details
- Conversation information
- Messages for each conversation (limited to 50 for performance)
- Participant details (client and seller/admin)

### Get Ticket Messages
```
GET /tickets/:ticketId/messages?page=1&limit=50
Authorization: Bearer <token>
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Messages per page (default: 50)
- `email`: Required for guest tickets

**Response:**
```json
{
  "status": "success",
  "results": 25,
  "pagination": {
    "currentPage": 1,
    "totalPages": 3,
    "total": 125,
    "hasNextPage": true,
    "hasPrevPage": false,
    "limit": 50
  },
  "data": {
    "messages": [...],
    "conversation": {...},
    "ticket": {
      "_id": "ticket_id",
      "ticketNumber": "TICKET-ABC123",
      "subject": "Issue with order",
      "status": "responded"
    }
  }
}
```

### Post Message to Ticket
```
POST /tickets/:ticketId/messages
Authorization: Bearer <token>
Content-Type: application/json

{
  "content": "Thank you for your inquiry. We'll resolve this shortly.",
  "attachments": ["url1", "url2"]
}
```

**Response:**
```json
{
  "status": "success",
  "data": {
    "message": {
      "_id": "message_id",
      "content": "Thank you for your inquiry...",
      "sender": {...},
      "createdAt": "2024-01-15T10:30:00.000Z"
    },
    "ticket": {
      "_id": "ticket_id",
      "ticketNumber": "TICKET-ABC123",
      "subject": "Issue with order",
      "status": "responded"
    }
  }
}
```

## Database Schema Integration

### Existing Models Used:
- **Ticket**: Links to conversation via `conversationId`
- **Conversation**: Manages participants and metadata
- **Message**: Stores individual messages
- **User**: Provides sender information

### Automatic Behaviors:
1. **Conversation Creation**: Automatically created when ticket is created (via `ticketUtils.js`)
2. **Status Updates**: Ticket status changes from 'open' to 'responded' when messages are posted
3. **Unread Counts**: Automatically updated in conversation when messages are sent
4. **Last Message**: Conversation's `lastMessage` field updated with latest message content

## Security Features

### Access Control:
- Users can only access their own ticket conversations
- Admins can access all ticket conversations
- Guest tickets require email verification
- JWT token required for all endpoints

### Data Validation:
- Message content is required and trimmed
- Ticket existence verification
- Conversation existence verification
- Proper error handling for all edge cases

## Performance Considerations

### Optimizations:
- Message pagination to prevent large data loads
- Limited message retrieval (50 messages) in ticket lists
- Efficient database queries with proper population
- Error handling to prevent crashes on missing data

### Database Queries:
- Uses MongoDB aggregation for efficient data retrieval
- Proper indexing on conversation and message collections
- Optimized population of related documents

## Testing Recommendations

### Manual Testing:
1. Create a ticket as a regular user
2. Verify conversation is automatically created
3. Post messages as both user and admin
4. Verify pagination works correctly
5. Test access control with different user roles
6. Verify guest ticket access with email parameter

### Automated Testing:
- Unit tests for controller functions
- Integration tests for API endpoints
- Authentication and authorization tests
- Database interaction tests

## Future Enhancements

### Potential Improvements:
1. Real-time messaging with WebSocket integration
2. File attachment support for messages
3. Message read receipts
4. Message search functionality
5. Conversation archiving
6. Automated responses for common queries
7. Message templates for admins
8. Conversation assignment to specific support agents
