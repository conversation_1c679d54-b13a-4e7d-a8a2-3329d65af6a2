import asyncHandler from "express-async-handler";
import User from "../models/userModel.js";
import jwt from "jsonwebtoken";
import bcrypt from "bcryptjs";
import AppError from "./../utils/appError.js";
import sendgrid from "@sendgrid/mail"; // Import SendGrid package
import moment from "moment";
import * as dotenv from "dotenv";
import Offer from "../models/offerModel.js";
import Order from "../models/OrderModel.js";

dotenv.config();
sendgrid.setApiKey(process.env.EMAIL_API_KEY);

// const login = asyncHandler(async (req, res, next) => {
//     try {
//         const { email, password } = req.body;
//         if (!email || !password) {
//             return next(new AppError('Email and password are required', 400));
//         }
//         const user = await User.findOne({ email });
//         if (!user) {
//             return next(new AppError('Invalid credentials', 401));
//         }
//         const isPasswordValid = await bcrypt.compare(password, user.password);
//         if (!isPasswordValid) {
//             return next(new AppError('Invalid credentials', 401));
//         }
//         const token = jwt.sign({ email: user.email }, process.env.JWT_SECRET);
//         res.status(200).json({ user, token });
//     } catch (error) {
//         next(error);
//     }
// });

// const register = asyncHandler(async (req, res, next) => {
//     try {
//         const { name, email, password, confirmPassword, ipAddress } = req.body;

//         if (!name || !email || !password || !confirmPassword) {
//             return next(new AppError('Name, email, password, and confirm password are required', 400));
//         }
//         if (password !== confirmPassword) {
//             return next(new AppError('Passwords do not match', 400));
//         }

//         const userExists = await User.findOne({ email });
//         if (userExists) {
//             return next(new AppError('User already exists', 400));
//         }

//         const ipExists = await User.findOne({ ipAddress });
//         if (ipExists) {
//             return next(new AppError('Only one user can be created from this IP address', 400));
//         }

//         const hashedPassword = await bcrypt.hash(password, 12);
//         const user = await User.create({
//             name,
//             email,
//             password: hashedPassword,
//             ipAddress
//         });

//         const token = jwt.sign({ email: user.email }, process.env.JWT_SECRET);
//         res.status(201).json({ user, token });
//     } catch (error) {
//         next(error);
//     }
// });
const login = asyncHandler(async (req, res, next) => {
  const { email, password, loginMethod, facebookID } = req.body;

  try {
    let user;

    // Facebook Login
    if (loginMethod === "facebook") {
      if (!facebookID) {
        return next(
          new AppError("Facebook ID is required for Facebook login", 400)
        );
      }
      user = await User.findOne({ facebookID, loginWithFacebook: true });
      if (!user) {
        return next(
          new AppError(
            "Invalid Facebook ID or user not registered with Facebook",
            401
          )
        );
      }
    }
    // Google Login
    else if (loginMethod === "google") {
      if (!email) {
        return next(new AppError("Email is required for Google login", 400));
      }

      user = await User.findOne({ email, loginWithGoogle: true });
      if (!user) {
        return next(
          new AppError("Invalid email or user not registered with Google", 401)
        );
      }
    }
    // Manual Login
    else {
      if (!email || !password) {
        return next(
          new AppError("Email and password are required for manual login", 400)
        );
      }

      user = await User.findOne({
        email,
        loginWithFacebook: false,
        loginWithGoogle: false,
      });
      if (!user) {
        return next(
          new AppError("Invalid email or user not registered manually", 401)
        );
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        return next(new AppError("Incorrect password", 401));
      }
    }

    const clearSaleLogin = await fetch(
        `${process.env.CLEARSALE_API_BASE}/auth/login`,
        {
          method: "POST",
          body: JSON.stringify({
            Login: {
              ApiKey: process.env.CLEARSALE_API_KEY,
              ClientId: process.env.CLEARSALE_CLIENT_ID,
              ClientSecret: process.env.CLEARSALE_CLIENT_SECRET,
            }
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
    );

    const clearSaleToken = await clearSaleLogin.json();

    // Generate Token
    const token = jwt.sign({ email: user.email }, process.env.JWT_SECRET, {
      expiresIn: "30d",
    });

    // Send Response
    res.status(200).json({
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        loginWithFacebook: user.loginWithFacebook,
        loginWithGoogle: user.loginWithGoogle,
        role: user.role,
      },
      token,
      clearSaleToken: clearSaleToken.Token.Value,
    });
  } catch (error) {
    next(error);
  }
});
const register = asyncHandler(async (req, res, next) => {
  try {
    const {
      name,
      email,
      password,
      confirmPassword,
      ipAddress,
      loginMethod,
      facebookID,
      role,
    } = req.body;

    if (!ipAddress) {
      return next(new AppError(" IP address is required", 400));
    }

    if (loginMethod === "facebook") {
      // Facebook registration logic
      if (!facebookID) {
        return next(
          new AppError("Facebook ID is required for Facebook login", 400)
        );
      }
    } else if (loginMethod === "google") {
      // Google registration logic
      if (!email) {
        return next(new AppError("Email is required for Google login", 400));
      }
    } else {
      // Manual registration logic
      if (!email || !password || !confirmPassword) {
        return next(
          new AppError(
            "Name, email, password, and confirm password are required",
            400
          )
        );
      }
      if (password !== confirmPassword) {
        return next(new AppError("Passwords do not match", 400));
      }
    }

    const queryConditions = [{ email }];
    if (loginMethod === "facebook" && facebookID) {
      queryConditions.push({ facebookID });
    }
    const userExists = await User.findOne({ $or: queryConditions });

    if (userExists) {
      return next(new AppError("User already exists", 400));
    }

    const ipExists = await User.findOne({ ipAddress });
    if (ipExists) {
      return next(
        new AppError("Only one user can be created from this IP address", 400)
      );
    }

    let hashedPassword;
    if (loginMethod !== "facebook" && loginMethod !== "google") {
      hashedPassword = await bcrypt.hash(password, 12);
    }

    // Create the user with the provided details
    const user = await User.create({
      name,
      email,
      password: hashedPassword,
      ipAddress,
      loginWithGoogle: loginMethod && loginMethod === "google",
      loginWithFacebook: loginMethod && loginMethod === "facebook",
      facebookID: loginMethod == "facebook" ? facebookID : undefined,
      role,
    });

    const clearSaleLogin = await fetch(
        `${process.env.CLEARSALE_API_BASE}/auth/login`,
        {
          method: "POST",
          body: JSON.stringify({
            Login: {
              ApiKey: process.env.CLEARSALE_API_KEY,
              ClientId: process.env.CLEARSALE_CLIENT_ID,
              ClientSecret: process.env.CLEARSALE_CLIENT_SECRET,
            }
          }),
          headers: {
            "Content-Type": "application/json",
          },
        },
    );

    const clearSaleToken = await clearSaleLogin.json();

    // Token logic
    const tokenExpiration = "30d";
    const token = jwt.sign({ email: user.email }, process.env.JWT_SECRET, {
      expiresIn: tokenExpiration,
    });
    res.status(201).json({ user, token, clearSaleToken: clearSaleToken.Token.Value });
  } catch (error) {
    next(error);
  }
});
const forgotPassword = asyncHandler(async (req, res, next) => {
  try {
    const { email } = req.body; // Extract email from request body

    // Find the user by email
    const user = await User.findOne({ email });
    if (!user) {
      return next(new AppError("No users found with this email", 404));
    }

    // Generate the reset password token
    const expires = moment().add(10, "minutes");
    const resetPasswordToken = jwt.sign(
      { id: user._id, exp: Math.floor(expires.valueOf() / 1000) }, // Set expiration in seconds
      process.env.JWT_SECRET // Your JWT secret from environment variables
    );

    // Prepare email body with the reset link
    const resetPasswordLink = `${process.env.FRONTEND_URL}?token=${resetPasswordToken}`; // Assuming you have a frontend URL

    const emailBody = `
            <h1>Password Reset Request</h1>
            <p>You requested to reset your password. Click the link below to reset it:</p>
            <a href="${resetPasswordLink}">Reset Password</a>
            <p>This link will expire in 10 minutes.</p>
        `;

    // Prepare the email data
    const msg = {
      to: email, // Recipient email
      from: "<EMAIL>", // Sender email (must be verified with SendGrid)
      subject: "Password Reset Request",
      html: emailBody, // HTML email content
    };

    // Send the email via SendGrid
    await sendgrid.send(msg);

    res.status(200).send({
      status: "success",
      message: "Reset password email sent successfully.",
    });
  } catch (error) {
    next(error);
  }
});
const resetPassword = asyncHandler(async (req, res, next) => {
  try {
    const { token } = req.query; // Extract token from query parameters
    const { password } = req.body; // New password from request body

    if (!token) {
      return next(new AppError("Reset token is missing.", 400));
    }

    // Verify the JWT token and extract user ID
    const decodedToken = jwt.verify(token, process.env.JWT_SECRET); // Verify token using the same JWT secret
    const userId = decodedToken.id; // Extract user ID from the token payload

    // Find the user by ID
    const user = await User.findById(userId);
    if (!user) {
      return next(new AppError("User not found.", 404));
    }

    // Hash the new password
    const hashedPassword = await bcrypt.hash(password, 8);

    // Update the user's password in the database
    user.password = hashedPassword;
    await user.save();

    // Respond with a success message
    res.status(200).send({
      status: "success",
      message: "Password reset successfully.",
      redirectURL: `${process.env.FRONTEND_URL}`, // Optionally redirect the user after reset
    });
  } catch (error) {
    next(error);
  }
});
const getMe = asyncHandler(async (req, res, next) => {
  try {
    // The `protect` middleware ensures `req.user` is set
    if (!req.user) {
      return next(new AppError("User not found", 404));
    }

    let sellerLevel = {};
    let offerCount = null;
    let sellerStats = {};

    if (req.user.role === "seller") {
      // Get seller level and offer count
      sellerLevel = await req.user.getSellerLevel();
      offerCount = await req.user.getOfferCount();

      // Get all offers by the seller
      const offers = await Offer.find({ seller: req.user._id });
      const offerIds = offers.map(offer => offer._id);

      // Calculate total ratings and positive ratings (4-5 stars)
      const ratingsStats = await Offer.aggregate([
        { $match: { _id: { $in: offerIds } } },
        { $unwind: "$ratings" },
        {
          $group: {
            _id: null,
            totalRatings: { $sum: 1 },
            positiveRatings: {
              $sum: {
                $cond: [{ $gte: ["$ratings.stars", 4] }, 1, 0]
              }
            },
            averageRating: { $avg: "$ratings.stars" }
          }
        }
      ]);

      // Get order statistics
      const orderStats = await Order.aggregate([
        {
          $match: {
            "items.offer": { $in: offerIds }
          }
        },
        {
          $group: {
            _id: null,
            totalOrders: { $sum: 1 },
            completedOrders: {
              $sum: {
                $cond: [{ $eq: ["$status", "completed"] }, 1, 0]
              }
            }
          }
        }
      ]);

      // Calculate statistics
      const stats = ratingsStats[0] || { totalRatings: 0, positiveRatings: 0, averageRating: 0 };
      const orders = orderStats[0] || { totalOrders: 0, completedOrders: 0 };
      
      const positiveFeedback = stats.totalRatings > 0 
        ? Math.round((stats.positiveRatings / stats.totalRatings) * 100) 
        : 0;
      
      const completionRate = orders.totalOrders > 0 
        ? Math.round((orders.completedOrders / orders.totalOrders) * 100) + "%" 
        : "0%";

      sellerStats = {
        totalOrders: orders.totalOrders,
        completedOrders: orders.completedOrders,
        averageRating: Math.round(stats.averageRating * 10) / 10 || 0,
        tier: sellerLevel.currentLevel?.name || "New Seller",
        completionRate,
        totalSales: orders.completedOrders,
        positiveFeedback: positiveFeedback + "%"
      };
    }

    // Respond with the user information, excluding sensitive data
    res.status(200).json({
      success: true,
      user: {
        ...req.user.toObject(),
        address: req.user.address || [],
        sellerLevel,
        offerCount,
        sellerStats
      },
    });
  } catch (error) {
    next(error);
  }
});

// const updateMe = asyncHandler(async (req, res, next) => {
//   try {
//     if (!req.user) {
//       return next(new AppError("User not found", 404));
//     }
//     // Update user with the provided fields
//     const updatedUser = await User.findByIdAndUpdate(req.user.id, req.body);
//     if (!updatedUser) {
//       return next(new AppError("Failed to update user", 500));
//     }

//     res.status(200).json({
//       success: true,
//       user: updatedUser,
//     });
//   } catch (error) {
//     next(error);
//   }
// });
const updateMe = asyncHandler(async (req, res, next) => {
  try {
    if (!req.user) {
      return next(new AppError("User not found", 404));
    }

    // // Check if the request is updating the address field
    // if (req.body.address) {
    //   const updatedUser = await User.findByIdAndUpdate(req.user.id, req.body);

    //   return res.status(200).json({
    //     success: true,
    //     user: updatedUser,
    //   });
    // }

    // For other fields, update normally
    const updatedUser = await User.findByIdAndUpdate(req.user.id, req.body, {
      new: true,
      runValidators: true,
    });

    res.status(200).json({
      success: true,
      user: updatedUser,
    });
  } catch (error) {
    next(error);
  }
});

export { register, login, forgotPassword, resetPassword, getMe, updateMe };
