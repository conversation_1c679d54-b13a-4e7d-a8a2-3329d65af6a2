import express from "express";
import {
  createBlog,
  getAllBlogs,
  getBlogById,
  updateBlog,
  deleteBlog,
  getAllFilters,
} from "./../controllers/blogController.js";
import { protect, admin } from "../middleware/authMiddleware.js";

const router = express.Router();

// Create a new blog (admin only)
router.route("/").post(protect, admin, createBlog).get(getAllBlogs);

// Get, update, or delete a blog by ID (admin only for update & delete)
router.route("/filters").get(getAllFilters);
router
  .route("/:id")
  .get(getBlogById)
  .patch(protect, admin, updateBlog)
  .delete(protect, admin, deleteBlog);

export default router;
