import express from "express";
import { protect } from "../middleware/authMiddleware.js";

import {
  register,
  login,
  forgotPassword,
  resetPassword,
  getMe,
  updateMe,
} from "./../controllers/userController.js";

const router = express.Router();
// Route to register a new user
router.get("/get-me", protect, getMe);
router.post("/register", register);
router.post("/login", login);
router.post("/forget-password", forgotPassword);
router.post("/reset-password", resetPassword);
router.patch("/update-me", protect, updateMe);

export default router;
