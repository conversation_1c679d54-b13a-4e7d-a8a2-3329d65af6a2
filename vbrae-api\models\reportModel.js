import mongoose from "mongoose";

const reportSchema = new mongoose.Schema(
  {
    problemType: {
      type: String,
      required: true,
      enum: ["Revoked", "Already Redeemed", "Other"],
      trim: true,
    },
    offerId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "Offer",
    },
    orderId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "Order",
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "User",
    },
    key: {
      type: String,
      required: true,
      trim: true,
    },
    preferredSolution: {
      type: String,
      enum: ["Refund in store Balance", "Provide new Key"],
      trim: true,
    },
    comment: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    attachments: {
      type: [String],
    },
    status: {
      type: String,
      enum: ["Pending", "In Progress", "Resolved", "Rejected"],
      default: "Pending",
    },
    priority: {
      type: String,
      enum: ["Low", "Medium", "High"],
      default: "Medium",
    },
    adminNotes: {
      type: String,
      trim: true,
    },
    resolvedAt: {
      type: Date,
    },
    resolvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
);

// Add validation for attachments array length
reportSchema.pre("validate", function (next) {
  if (this.attachments && this.attachments.length > 3) {
    next(new Error("Maximum 3 attachments allowed"));
  }
  next();
});

reportSchema.index({ userId: 1 });
reportSchema.index({ orderId: 1 });
reportSchema.index({ status: 1 });
reportSchema.index({ createdAt: -1 });

const Report = mongoose.model("Report", reportSchema);
export default Report;
