import mongoose from "mongoose";

const watchlistSchema = new mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    offer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Offer",
      required: true,
    },
    createdAt: {
      type: Date,
      default: Date.now,
      required: true,
    },
    expiresAt: {
      type: Date,
      default: function () {
        return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
      },
      required: true,
      index: { expires: 0 }, // TTL index for auto-removal
    },
    // Add more fields if needed for tracking activity, etc.
  },
  {
    timestamps: true,
  }
);

// Ensure a user can't add the same offer to their watchlist multiple times
watchlistSchema.index({ user: 1, offer: 1 }, { unique: true });

export default mongoose.model("Watchlist", watchlistSchema); 