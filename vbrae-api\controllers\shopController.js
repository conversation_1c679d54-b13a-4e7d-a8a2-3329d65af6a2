import mongoose from "mongoose";
import Shop from "../models/shopModel.js";
import Offer from "../models/offerModel.js";
import Order from "../models/OrderModel.js";
import Cart from "../models/cartModel.js";

import User from "../models/userModel.js";
import * as factory from "./handlerFactory.js";
import asyncHandler from "express-async-handler";

import bcrypt from "bcryptjs";

// Get the seller's shop
export const getSellerShop = asyncHandler(async (req, res) => {
  const shop = await Shop.findOne({
    seller: req.user._id,
  }).populate("seller");

  if (!shop) {
    return res.status(404).json({ message: "Shop not found for this seller" });
  }

  res.status(200).json(shop);
});
// Create a new shop
export const createShop = factory.createOne(Shop);

// Get all shops
export const getAllShops = factory.getAll("", Shop, [{ path: "seller" }]);

// Get a shop by ID
export const getShopById = factory.getOne(Shop, [
  { path: "seller" },
  { path: "followers" },
]);

// Update a shop by ID
// export const updateShop = factory.updateOne(Shop);

// Delete a shop by ID
export const deleteShop = factory.deleteOne(Shop);
// export const updateUserAndShop = asyncHandler(async (req, res) => {
//   const {
//     email,
//     name,
//     avatar,
//     enable2FA,
//     newPassword,
//     shopName,
//     coverImage,
//     vacationMode,
//     sendEmailOnNewOrder,
//     showLocation,
//     phoneNumber,
//     links,
//     hideAdditionalInfo,
//     currentPassword,
//     country,
//   } = req.body;

//   // Fetch User & Shop
//   let [user, shop] = await Promise.all([
//     User.findById(req.user._id),
//     Shop.findById(req.params.id),
//   ]);

//   if (!user || !shop) {
//     return res.status(404).json({ message: "User or Shop not found" });
//   }

//   // Update User fields
//   Object.assign(user, { email, name, avatar, enable2FA });

//   // Update Shop fields
//   Object.assign(shop, {
//     shopName,
//     coverImage,
//     vacationMode,
//     sendEmailOnNewOrder,
//     showLocation,
//     phoneNumber,
//     links,
//     hideAdditionalInfo,
//   });

//   // Update Password if provided
//   if (newPassword) {
//     // const salt = await bcrypt.genSalt(10);
//     user.password = await bcrypt.hash(newPassword, 12);
//   }

//   // Save updates
//   await Promise.all([user.save(), shop.save()]);

//   res.json({ message: "Profile updated successfully", user, shop });
// });
export const updateUserAndShop = asyncHandler(async (req, res) => {
  const {
    email,
    name,
    avatar,
    enable2FA,
    newPassword,
    shopName,
    coverImage,
    vacationMode,
    sendEmailOnNewOrder,
    showLocation,
    phoneNumber,
    links,
    hideAdditionalInfo,
    currentPassword,
    country,
    companyTaxId,
    telephone,
    fax,
    addresses,
  } = req.body;

  // Fetch User & Shop
  const [user, shop] = await Promise.all([
    User.findById(req.user._id),
    Shop.findById(req.params.id),
  ]);

  if (!user || !shop) {
    return res.status(404).json({ message: "User or Shop not found" });
  }

  // Update User fields only if provided
  if (email) user.email = email;
  if (name) user.name = name;
  if (avatar) user.avatar = avatar;
  if (enable2FA !== undefined) user.enable2FA = enable2FA;
  if (country) user.country = country;
  if (phoneNumber) user.phoneNumber = phoneNumber;

  // Update Shop fields only if provided
  if (shopName) shop.shopName = shopName;
  if (coverImage) shop.coverImage = coverImage;
  if (vacationMode !== undefined) shop.vacationMode = vacationMode;
  if (sendEmailOnNewOrder !== undefined)
    shop.sendEmailOnNewOrder = sendEmailOnNewOrder;
  if (showLocation !== undefined) shop.showLocation = showLocation;
  if (phoneNumber) shop.phoneNumber = phoneNumber;
  if (links) shop.links = links;
  if (addresses) shop.addresses = addresses;
  if (hideAdditionalInfo !== undefined)
    shop.hideAdditionalInfo = hideAdditionalInfo;
  if (companyTaxId) shop.companyTaxId = companyTaxId;
  if (telephone) shop.telephone = telephone;
  if (fax) shop.fax = fax;

  // Update Password if provided
  if (newPassword) {
    if (!currentPassword) {
      return res.status(400).json({
        message: "Current password is required to set a new password",
      });
    }

    const isMatch = await bcrypt.compare(currentPassword, user.password);
    if (!isMatch) {
      return res.status(400).json({ message: "Current password is incorrect" });
    }

    user.password = await bcrypt.hash(newPassword, 12);
  }

  // Save updates
  await Promise.all([user.save(), shop.save()]);

  res.json({ message: "Profile updated successfully", user, shop });
});

export const addFollower = async (req, res) => {
  const { shopId } = req.params;
  const { userId } = req.body;

  try {
    const shop = await Shop.findById(shopId);
    if (!shop) {
      return res.status(404).json({ message: "Shop not found" });
    }

    // Check if the user is already a follower
    if (shop.followers.includes(userId)) {
      return res
        .status(400)
        .json({ message: "User already follows this shop" });
    }

    // Add the user to the followers list
    shop.followers.push(userId);
    await shop.save();

    res.status(200).json({ message: "Follower added successfully", shop });
  } catch (error) {
    res.status(500).json({ message: "Error adding follower", error });
  }
};

export const removeFollower = async (req, res) => {
  const { shopId } = req.params;
  const { userId } = req.body;

  try {
    const shop = await Shop.findById(shopId);
    if (!shop) {
      return res.status(404).json({ message: "Shop not found" });
    }

    // Check if the user is actually following the shop
    if (!shop.followers.includes(userId)) {
      return res
        .status(400)
        .json({ message: "User is not following this shop" });
    }

    // Remove the user from the followers list
    shop.followers = shop.followers.filter((id) => id.toString() !== userId);
    await shop.save();

    res.status(200).json({ message: "Follower removed successfully", shop });
  } catch (error) {
    res.status(500).json({ message: "Error removing follower", error });
  }
};

export const toggleFollow = async (req, res) => {
  const { shopId } = req.params;
  const { userId } = req.body;

  try {
    const shop = await Shop.findById(shopId);
    if (!shop) {
      return res.status(404).json({ message: "Shop not found" });
    }

    const isFollowing = shop.followers.includes(userId);

    if (isFollowing) {
      // Unfollow: Remove user from followers list
      shop.followers = shop.followers.filter((id) => id.toString() !== userId);
      await shop.save();
      return res.status(200).json({ message: "Unfollowed successfully", shop });
    } else {
      // Follow: Add user to followers list
      shop.followers.push(userId);
      await shop.save();
      return res.status(200).json({ message: "Followed successfully", shop });
    }
  } catch (error) {
    res.status(500).json({ message: "Error toggling follow status", error });
  }
};

export const checkFollowStatus = async (req, res) => {
  const { shopId } = req.params;
  const { userId } = req.body; // Or you can pass userId as a query param if needed

  try {
    const shop = await Shop.findById(shopId);
    if (!shop) {
      return res.status(404).json({ message: "Shop not found" });
    }

    const isFollowing = shop.followers.includes(userId);

    res.status(200).json({ isFollowing });
  } catch (error) {
    res.status(500).json({ message: "Error checking follow status", error });
  }
};

// export const dashboard = async (req, res) => {
//   try {
//     const sellerId = req.params.sellerId;
//     // Get shop and offers in parallel
//     const [seller, shop, offers] = await Promise.all([
//       User.findById(sellerId).select("name email avatar"),
//       Shop.findOne({ seller: sellerId }).select("followers"),
//       Offer.find({ seller: sellerId }).select("_id"),
//     ]);

//     const offerIds = offers.map((offer) => offer._id);

//     // Parallel processing of data
//     const [totalOrders, topBuyers, ratingsAggregate] = await Promise.all([
//       // Total orders count
//       Order.countDocuments({ "items.offer": { $in: offerIds } }),

//       // Top buyers aggregation (fixed projection)
//       Order.aggregate([
//         { $match: { "items.offer": { $in: offerIds } } },
//         { $group: { _id: "$user", count: { $sum: 1 } } },
//         { $sort: { count: -1 } },
//         { $limit: 5 },
//         {
//           $lookup: {
//             from: "users",
//             localField: "_id",
//             foreignField: "_id",
//             as: "user",
//           },
//         },
//         { $unwind: "$user" },
//         {
//           $project: {
//             "user._id": 1,
//             "user.name": 1,
//             "user.email": 1,
//             "user.avatar": 1,
//             count: 1,
//           },
//         },
//       ]),

//       // Ratings aggregation with fallback
//       Offer.aggregate([
//         { $match: { seller: sellerId } },
//         { $unwind: "$ratings" },
//         {
//           $group: {
//             _id: null,
//             totalReviews: { $sum: 1 },
//             averageRating: { $avg: "$ratings.stars" },
//           },
//         },
//       ]),
//     ]);

//     // Handle ratings results safely
//     const ratingsResult = ratingsAggregate[0] || {
//       totalReviews: 0,
//       averageRating: 0,
//     };

//     // Calculate tier based on sales
//     let tier = "New Seller";
//     const tiers = [
//       { min: 50, max: 149, name: "1st" },
//       { min: 150, max: 299, name: "2nd" },
//       { min: 300, max: 599, name: "3rd" },
//       { min: 600, max: 999, name: "4th" },
//       { min: 1000, max: Infinity, name: "5th" },
//     ];

//     if (totalOrders >= 50) {
//       const currentTier = tiers.find(
//         (t) => totalOrders >= t.min && totalOrders <= t.max
//       );
//       tier = currentTier ? currentTier.name : tier;
//     }

//     // Prepare response
//     const response = {
//       followersCount: shop?.followers?.length || 0,
//       totalOrders,
//       topBuyers: topBuyers.map((buyer) => ({
//         user: {
//           _id: buyer.user._id,
//           name: buyer.user.name,
//           email: buyer.user.email,
//           avatar: buyer.user.avatar,
//         },
//         orderCount: buyer.count,
//       })),
//       totalReviews: ratingsResult.totalReviews,
//       averageRating: ratingsResult.averageRating.toFixed(1),
//       tier,
//       seller,
//     };

//     res.status(200).json(response);
//   } catch (error) {
//     console.error("Dashboard error:", error);
//     res.status(500).json({
//       message: "Error fetching dashboard data",
//       error: error.message,
//     });
//   }
// };

// export const dashboard = async (req, res) => {
//   try {
//     const sellerId = req.params.sellerId;

//     // Get seller info, shop, and offers in parallel
//     const [seller, shop, offers] = await Promise.all([
//       User.findById(sellerId).select("name email avatar"),
//       Shop.findOne({ seller: sellerId }).select("followers"),
//       Offer.find({ seller: sellerId }).select("_id"),
//     ]);

//     const offerIds = offers.map((offer) => offer._id);

//     // Parallel processing of data including followers and reviews
//     const [totalOrders, topBuyers, ratingsAggregate, followersList] =
//       await Promise.all([
//         Order.countDocuments({ "items.offer": { $in: offerIds } }),

//         Order.aggregate([
//           { $match: { "items.offer": { $in: offerIds } } },
//           { $group: { _id: "$user", count: { $sum: 1 } } },
//           { $sort: { count: -1 } },
//           { $limit: 5 },
//           {
//             $lookup: {
//               from: "users",
//               localField: "_id",
//               foreignField: "_id",
//               as: "user",
//             },
//           },
//           { $unwind: "$user" },
//           {
//             $project: {
//               "user._id": 1,
//               "user.name": 1,
//               "user.email": 1,
//               "user.avatar": 1,
//               count: 1,
//             },
//           },
//         ]),

//         // Modified ratings aggregation using seller name
//         Offer.aggregate([
//           {
//             $match: {
//               "ratings.sellerName": seller.name,
//             },
//           },
//           { $unwind: "$ratings" },
//           {
//             $match: {
//               "ratings.sellerName": seller.name,
//             },
//           },
//           {
//             $group: {
//               _id: null,
//               totalReviews: { $sum: 1 },
//               averageRating: { $avg: "$ratings.stars" },
//             },
//           },
//         ]),

//         User.find({
//           _id: { $in: shop?.followers || [] },
//         }).select("name avatar"),
//       ]);

//     // Handle ratings results
//     const ratingsResult = ratingsAggregate[0] || {
//       totalReviews: 0,
//       averageRating: 0,
//     };

//     // Tier calculation remains the same
//     let tier = "New Seller";
//     const tiers = [
//       { min: 50, max: 149, name: "1st" },
//       { min: 150, max: 299, name: "2nd" },
//       { min: 300, max: 599, name: "3rd" },
//       { min: 600, max: 999, name: "4th" },
//       { min: 1000, max: Infinity, name: "5th" },
//     ];

//     if (totalOrders >= 50) {
//       const currentTier = tiers.find(
//         (t) => totalOrders >= t.min && totalOrders <= t.max
//       );
//       tier = currentTier ? currentTier.name : tier;
//     }

//     // Prepare response
//     const response = {
//       seller: {
//         ...seller.toObject(),
//         shopName: shop?.shopName || "",
//       },
//       followers: {
//         count: shop?.followers?.length || 0,
//         list: followersList,
//       },
//       totalOrders,
//       topBuyers: topBuyers.map((buyer) => ({
//         user: buyer.user,
//         orderCount: buyer.count,
//       })),
//       reviews: {
//         total: ratingsResult.totalReviews,
//         average: ratingsResult.averageRating.toFixed(1),
//       },
//       tier,
//     };

//     res.status(200).json(response);
//   } catch (error) {
//     console.error("Dashboard error:", error);
//     res.status(500).json({
//       message: "Error fetching dashboard data",
//       error: error.message,
//     });
//   }
// };

export const dashboard = async (req, res) => {
  try {
    const sellerId = req.params.sellerId;
    const monthlyStatsYear = new Date(req.query.year).getFullYear() || new Date().getFullYear();

    // Get seller info, shop, and offers in parallel
    const [seller, shop, offers] = await Promise.all([
      User.findById(sellerId).select("name email avatar createdAt"),
      Shop.findOne({ seller: sellerId }).select("followers shopName"),
      Offer.find({ seller: sellerId }).select("_id"),
    ]);

    const offerIds = offers.map((offer) => offer._id);

    // Parallel processing of data including order counts
    const [ordersSummary, topBuyers, ratingsAggregate, followersList, monthlyOrderStats] =
      await Promise.all([
        // Get order statistics in single aggregation
        Order.aggregate([
          {
            $match: {
              "items.offer": { $in: offerIds },
            },
          },
          {
            $group: {
              _id: null,
              total: { 
                $sum: 1
              },
              revenue: {
                $sum: {
                  $cond: [{ $in: ['$status', ['completed', 'review']] }, '$total', 0]
                }
              },
              sellerRevenue: {
                $sum: {
                  $cond: [{ $in: ['$status', ['completed', 'review']] }, '$subtotal', 0]
                }
              },
              pendingRevenue: {
                $sum: {
                  $cond: [{ $eq: ['$status', 'pending'] }, '$subtotal', 0]
                }
              },
              completed: {
                $sum: {
                  $cond: [{ $in: ["$status", ['completed', 'review']] }, 1, 0],
                },
              },
            },
          },
        ]),

        // Top buyers aggregation
        Order.aggregate([
          { $match: { "items.offer": { $in: offerIds } } },
          { $group: { _id: "$user", count: { $sum: 1 } } },
          { $sort: { count: -1 } },
          { $limit: 5 },
          {
            $lookup: {
              from: "users",
              localField: "_id",
              foreignField: "_id",
              as: "user",
            },
          },
          { $unwind: "$user" },
          {
            $project: {
              "user._id": 1,
              "user.name": 1,
              "user.email": 1,
              "user.avatar": 1,
              count: 1,
            },
          },
        ]),

        // Modified ratings aggregation using seller name
        Offer.aggregate([
          {
            $match: {
              "ratings.sellerName": seller.name,
            },
          },
          { $unwind: "$ratings" },
          {
            $match: {
              "ratings.sellerName": seller.name,
            },
          },
          {
            $group: {
              _id: null,
              totalReviews: { $sum: 1 },
              averageRating: { $avg: "$ratings.stars" },
            },
          },
        ]),

        // Followers list
        User.find({
          _id: { $in: shop?.followers || [] },
        }).select("name avatar"),

        // Get Monthly Stats
        Order.aggregate([
          {
            $match: {
              "items.offer": { $in: offerIds },
              $expr: { $eq: [{ $year: '$createdAt' }, monthlyStatsYear] },
              status: { $in: ["completed", 'review'] }, // Exclude cancelled orders
            },
          },
          {
            $group: {
              _id: { month: { $month: '$createdAt' } },
              total: {
                $sum: 1
              },
              revenue: {
                $sum: {
                  $cond: [{ $in: ['$status', ['completed', 'review']] }, '$total', 0]
                }
              },
              sellerRevenue: {
                $sum: {
                  $cond: [{ $in: ['$status', ['completed', 'review']] }, '$subtotal', 0]
                }
              },
            },
          },
          { $sort: { '_id.month': 1 } }
        ]),
      ]);

    // Extract order counts
    const orderData = ordersSummary[0] || { total: 0, completed: 0 };
    const totalOrders = orderData.total;
    const completedOrders = orderData.completed;

    // Handle ratings results
    const ratingsResult = ratingsAggregate[0] || {
      totalReviews: 0,
      averageRating: 0,
    };

    // Tier calculation (now based on completed orders)
    let tier = "New Seller";
    const tiers = [
      { min: 50, max: 149, name: "1st" },
      { min: 150, max: 299, name: "2nd" },
      { min: 300, max: 599, name: "3rd" },
      { min: 600, max: 999, name: "4th" },
      { min: 1000, max: Infinity, name: "5th" },
    ];

    if (completedOrders >= 50) {
      const currentTier = tiers.find(
        (t) => completedOrders >= t.min && completedOrders <= t.max
      );
      tier = currentTier ? currentTier.name : tier;
    }

    const updatedUser = await User.findByIdAndUpdate(
      sellerId,
      {
        sellerStats: {
          totalOrders,
          completedOrders,
          averageRating: ratingsResult.averageRating.toFixed(1),
          tier,
        },
      },
      { new: true, select: "sellerStats" }
    );

    // Prepare response
    const response = {
      seller: {
        ...seller.toObject(),
        shopName: shop?.shopName || "",
        shopId: shop?._id,
      },
      followers: {
        count: shop?.followers?.length || 0,
        list: followersList,
      },
      orders: {
        total: totalOrders,
        revenue: orderData.revenue,
        sellerRevenue: orderData.sellerRevenue,
        pendingRevenue: orderData.pendingRevenue,
        completed: completedOrders,
        completionRate:
          ((completedOrders / (totalOrders || 1)) * 100).toFixed(1) + "%",
      },
      monthlyOrderStats,
      topBuyers: topBuyers.map((buyer) => ({
        user: buyer.user,
        orderCount: buyer.count,
      })),
      reviews: {
        total: ratingsResult.totalReviews,
        average: ratingsResult.averageRating.toFixed(1),
      },
      tier,
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Dashboard error:", error);
    res.status(500).json({
      message: "Error fetching dashboard data",
      error: error.message,
    });
  }
};

// export const getBestSellingOffers = async (req, res) => {
//   try {
//     const sellerId = req.params.sellerId;
//     const userId = req.query.userId; // Get user ID from query params

//     // Get all offers from this seller first
//     const sellerOffers = await Offer.find({ seller: sellerId }).select("_id");
//     const offerIds = sellerOffers.map((offer) => offer._id);

//     // Get user's cart if userId is provided
//     let cartOfferIds = new Set();
//     if (userId) {
//       const cart = await Cart.findOne({ user: userId })
//         .populate("items.offer")
//         .lean();

//       if (cart && cart.items) {
//         cartOfferIds = new Set(
//           cart.items.map((item) => item.offer._id.toString())
//         );
//       }
//     }

//     // Aggregation pipeline to calculate total quantities
//     const bestSellers = await Order.aggregate([
//       { $unwind: "$items" }, // Break down the items array
//       {
//         $match: {
//           "items.offer": { $in: offerIds },
//           status: { $nin: ["cancelled"] }, // Exclude cancelled orders
//         },
//       },
//       {
//         $group: {
//           _id: "$items.offer",
//           totalSold: { $sum: "$items.quantity" },
//           totalRevenue: {
//             $sum: { $multiply: ["$items.quantity", "$items.price"] },
//           },
//         },
//       },
//       { $sort: { totalSold: -1 } }, // Sort by most sold first
//       {
//         $lookup: {
//           from: "offers",
//           localField: "_id",
//           foreignField: "_id",
//           as: "offerDetails",
//         },
//       },
//       { $unwind: "$offerDetails" },
//       {
//         $project: {
//           _id: 0,
//           offer: "$offerDetails",
//           totalSold: 1,
//           totalRevenue: 1,
//         },
//       },
//     ]);

//     if (!bestSellers.length) {
//       return res.status(200).json({
//         message: "No sales data available for this seller",
//         bestSellers: [],
//       });
//     }

//     // Convert to plain objects and add inCart status
//     const offersWithCartStatus = bestSellers.map((item) => {
//       const offer = item.offer.toObject ? item.offer.toObject() : item.offer;
//       return {
//         ...item,
//         offer: {
//           ...offer,
//           inCart: cartOfferIds.has(offer._id.toString()),
//         },
//       };
//     });

//     const allOffers = offersWithCartStatus.map(item=> item.offer);

//     res.status(200).json({
//       bestSellers: allOffers,
//     });
//   } catch (error) {
//     console.error("Best selling error:", error);
//     res.status(500).json({
//       message: "Error fetching best selling offers",
//       error: error.message,
//     });
//   }
// };

export const getBestSellingOffers = async (req, res) => {
  try {
    const sellerId = req.params.sellerId;
    const userId = req.query.userId;

    // Get all offers from this seller first
    const sellerOffers = await Offer.find({ seller: sellerId }).select("_id");
    const offerIds = sellerOffers.map((offer) => offer._id);

    // Get user's cart if userId is provided
    let cartOfferIds = new Set();
    if (userId) {
      const cart = await Cart.findOne({ user: userId })
        .populate("items.offer")
        .lean();

      if (cart?.items) {
        cartOfferIds = new Set(
          cart.items.map((item) => item.offer._id.toString())
        );
      }
    }

    // Updated aggregation pipeline with template population
    const bestSellers = await Order.aggregate([
      { $unwind: "$items" },
      {
        $match: {
          "items.offer": { $in: offerIds },
          status: { $nin: ["cancelled"] },
        },
      },
      {
        $group: {
          _id: "$items.offer",
          totalSold: { $sum: "$items.quantity" },
          totalRevenue: {
            $sum: { $multiply: ["$items.quantity", "$items.price"] },
          },
        },
      },
      { $sort: { totalSold: -1 } },
      {
        $lookup: {
          from: "offers",
          localField: "_id",
          foreignField: "_id",
          as: "offerDetails",
        },
      },
      { $unwind: "$offerDetails" },
      // Add template lookup
      {
        $lookup: {
          from: "templates",
          localField: "offerDetails.template",
          foreignField: "_id",
          as: "templateDetails",
        },
      },
      { $unwind: "$templateDetails" },
      // Project the final structure
      {
        $project: {
          _id: 0,
          offer: {
            $mergeObjects: [
              "$offerDetails",
              { template: "$templateDetails" },
              { sold: "$totalSold" }, // Add calculated sold count
            ],
          },
          totalSold: 1,
          totalRevenue: 1,
        },
      },
    ]);

    if (!bestSellers.length) {
      return res.status(200).json({
        message: "No sales data available for this seller",
        bestSellers: [],
      });
    }

    // Convert to plain objects and add inCart status
    const offersWithCartStatus = bestSellers.map((item) => {
      const offer = item.offer.toObject ? item.offer.toObject() : item.offer;
      return {
        ...item,
        offer: {
          ...offer,
          inCart: cartOfferIds.has(offer._id.toString()),
          // Ensure stock is calculated correctly
          stock: offer.licenseKeys?.length || 0,
        },
      };
    });

    const allOffers = offersWithCartStatus.map((item) => item.offer);

    res.status(200).json({
      bestSellers: allOffers,
    });
  } catch (error) {
    console.error("Best selling error:", error);
    res.status(500).json({
      message: "Error fetching best selling offers",
      error: error.message,
    });
  }
};
