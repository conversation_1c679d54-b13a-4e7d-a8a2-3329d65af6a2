import mongoose from 'mongoose';

const notificationSchema = new mongoose.Schema({
    userId: { 
        type: mongoose.Schema.Types.ObjectId, 
        ref: 'User', 
        required: true 
    },
    title: { type: String, required: true },
    message: { type: String, required: true },
    isRead: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },

    entityType: {
        type: String,
        enum: ['order', 'review', 'conversation-offer', 'conversation-ticket', 'kyc'],
        required: true,
    },
    entityId: {
        type: mongoose.Schema.Types.ObjectId,
        required: false, // can be null for system/promo
    },

    senderId: {
        type: mongoose.Schema.Types.ObjectId,
        required: false,
    }
});

const NotificationSchema = mongoose.model('Notification', notificationSchema);

export default NotificationSchema;