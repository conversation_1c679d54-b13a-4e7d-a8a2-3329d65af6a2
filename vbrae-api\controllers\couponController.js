import Coupon from "../models/couponModel.js";
import * as factory from "./handlerFactory.js";
import Cart from "../models/cartModel.js";

export const createCoupon = factory.createOne(Coupon);
export const getCoupon = factory.getOne(Coupon);
export const getAllCoupons = factory.getAll("coupons", Coupon, [], "code");
export const updateCoupon = factory.updateOne(Coupon);
export const deleteCoupon = factory.deleteOne(Coupon);

export const applyCoupon = async (req, res, next) => {
  try {
    const { code } = req.body;
    const userId = req.user._id;

    // Find the coupon
    const coupon = await Coupon.findOne({ code });
    if (!coupon) {
      return res.status(404).json({
        status: "error",
        message: "Coupon not found"
      });
    }

    // Check if user can use the coupon
    const canUse = await coupon.canUseCoupon(userId);
    if (!canUse) {
      return res.status(400).json({
        status: "error",
        message: "Coupon cannot be used"
      });
    }

    // Get user's cart
    const cart = await Cart.findOne({ user: userId }).populate({
      path: "items.offer",
      populate: {
        path: "seller"
      }
    });

    if (!cart || !cart.items.length) {
      return res.status(400).json({
        status: "error",
        message: "Cart is empty"
      });
    }

    // Filter items that belong to the coupon seller
    const applicableItems = cart.items.filter(
      item => item.offer.seller._id.toString() === coupon.seller.toString()
    );

    if (!applicableItems.length) {
      return res.status(400).json({
        status: "error",
        message: "No items in cart from this seller"
      });
    }

    // Calculate total for applicable items
    const subtotal = applicableItems.reduce(
      (sum, item) => sum + (item.offer.customerPays * item.quantity),
      0
    );

    // Check minimum order amount
    if (subtotal < coupon.minOrderAmount) {
      return res.status(400).json({
        status: "error",
        message: `Minimum order amount of ${coupon.minOrderAmount} required`
      });
    }

    // Calculate discount
    let discount = 0;
    if (coupon.discountType === "fixed") {
      discount = Math.min(coupon.discountValue, subtotal);
    } else {
      discount = (subtotal * coupon.discountValue) / 100;
    }

    // Apply discount to cart
    cart.coupon = {
      code: coupon.code,
      discount,
      discountType: coupon.discountType,
      discountValue: coupon.discountValue,
      seller: coupon.seller
    };

    // Update cart total
    cart.total = cart.items.reduce(
      (sum, item) => sum + (item.offer.customerPays * item.quantity),
      0
    ) - discount;

    // Update usage count
    const userUsage = coupon.usedBy.find(
      usage => usage.user.toString() === userId.toString()
    );

    if (userUsage) {
      userUsage.usageCount += 1;
    } else {
      coupon.usedBy.push({
        user: userId,
        usageCount: 1
      });
    }

    coupon.totalCoupons -= 1;
    await coupon.save();
    await cart.save();

    res.status(200).json({
      status: "success",
      data: {
        cart,
        discount,
        subtotal
      }
    });

  } catch (err) {
    next(err);
  }
};
