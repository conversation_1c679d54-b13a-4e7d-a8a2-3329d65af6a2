import mongoose from "mongoose";

const shopSchema = new mongoose.Schema(
  {
    seller: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true, // Every shop must belong to a seller
    },
    shopName: { type: String, required: true },
    telephone: { type: String },
    fax: { type: String },
    shopDescription: { type: String },
    coverImage: { type: String, default: "" }, // URL of cover image
    sendEmailOnNewOrder: { type: Boolean, default: false }, // Checkbox: Notify seller
    showLocation: { type: Boolean, default: false }, // Checkbox: Show shop location
    vacationMode: { type: Boolean, default: false }, // Toggle for vacation
    tier: {
      type: String,
    },
    links: [
      {
        title: { type: String },
        url: { type: String },
      },
    ], // List of links
    addresses: [
      {
        street: { type: String },
        city: { type: String },
        state: { type: String },
        zip: { type: String },
        country: { type: String },
      },
    ], // Multiple addresses
    companyTaxId: { type: String },
    followers: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
      },
    ],
    // fax: { type: String },
    // telephone: { type: String },
  },
  { timestamps: true }, // Adds createdAt and updatedAt timestamps
);

export default mongoose.model("Shop", shopSchema);
