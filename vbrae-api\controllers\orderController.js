import asyncHandler from "express-async-handler";
import User from "../models/userModel.js";
import Order from "./../models/OrderModel.js";
import Shop from "../models/shopModel.js";
import Offer from "./../models/offerModel.js";
import mongoose from "mongoose";
import { notifyUser } from "../services/notificationService.js";
import sendgrid from "@sendgrid/mail";
import { orderCompletionMail } from "../utils/emailTemplates/templates.js";
sendgrid.setApiKey(process.env.EMAIL_API_KEY);
// export const getSellerOrders = async (req, res) => {
//   const sellerId = req.user._id;

//   try {
//     const orders = await Order.aggregate([
//       {
//         $unwind: "$items",
//       },
//       {
//         $lookup: {
//           from: "offers",
//           localField: "items.offer",
//           foreignField: "_id",
//           as: "offerDetails",
//         },
//       },
//       {
//         $unwind: "$offerDetails",
//       },
//       {
//         $match: {
//           "offerDetails.seller": mongoose.Types.ObjectId(sellerId),
//         },
//       },
//       {
//         $lookup: {
//           from: "templates",
//           localField: "offerDetails.template",
//           foreignField: "_id",
//           as: "templateDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$templateDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $addFields: {
//           "offerDetails.coverImage": "$templateDetails.coverImage",
//         },
//       },
//       {
//         $group: {
//           _id: "$_id",
//           user: { $first: "$user" },
//           items: {
//             $push: {
//               offer: "$items.offer",
//               quantity: "$items.quantity",
//               price: "$items.price",
//               keys: "$items.keys",
//               _id: "$items._id",
//               offerDetails: "$offerDetails",
//             },
//           },
//           subtotal: { $first: "$subtotal" },
//           serviceFee: { $first: "$serviceFee" },
//           total: { $first: "$total" },
//           savings: { $first: "$savings" },
//           status: { $first: "$status" },
//           latest: { $first: "$latest" },
//           createdAt: { $first: "$createdAt" },
//           updatedAt: { $first: "$updatedAt" },
//           orderIncrementId: { $first: "$orderIncrementId" },
//           reservationId: { $first: "$reservationId" },
//           orderNumber: { $first: "$orderNumber" },
//           __v: { $first: "$__v" },
//         },
//       },
//       // Add user details lookup here
//       {
//         $lookup: {
//           from: "users",
//           localField: "user",
//           foreignField: "_id",
//           as: "userDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$userDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $project: {
//           user: 1,
//           items: 1,
//           subtotal: 1,
//           serviceFee: 1,
//           total: 1,
//           savings: 1,
//           status: 1,
//           latest: 1,
//           createdAt: 1,
//           updatedAt: 1,
//           orderIncrementId: 1,
//           reservationId: 1,
//           orderNumber: 1,
//           __v: 1,
//           userDetails: {
//             _id: 1,
//             name: 1,
//             email: 1,
//             username: 1,
//             createdAt: 1,
//             updatedAt: 1,
//           },
//         },
//       },
//       {
//         $group: {
//           _id: null,
//           totalOrders: { $sum: 1 },
//           completedOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
//           },
//           pendingOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
//           },
//           reviewOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "review"] }, 1, 0] },
//           },
//           refundOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "refund"] }, 1, 0] },
//           },
//           cancelledOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
//           },
//           orders: { $push: "$$ROOT" },
//         },
//       },
//       {
//         $project: {
//           _id: 0,
//           totalOrders: 1,
//           completedOrders: 1,
//           pendingOrders: 1,
//           reviewOrders: 1,
//           refundOrders: 1,
//           cancelledOrders: 1,
//           orders: 1,
//         },
//       },
//     ]);
//     const result = orders[0] || {
//       totalOrders: 0,
//       completedOrders: 0,
//       pendingOrders: 0,
//       reviewOrders: 0,
//       refundOrders: 0,
//       cancelledOrders: 0,
//       orders: [],
//     };
//     res.status(200).json(result);
//   } catch (error) {
//     res
//       .status(500)
//       .json({ message: "Error fetching orders", error: error.message });
//   }
// };

// export const getSellerOrders = async (req, res) => {
//   const sellerId = req.user._id;
//   const {
//     productName,
//     orderIncrementId,
//     createdDateFrom,
//     createdDateTo,
//     releaseDateFrom,
//     releaseDateTo,
//     reservationId,
//     status,
//   } = req.query;

//   try {
//     // Build the orderMatch object based on query parameters
//     const orderMatch = {};

//     // Status filter
//     if (status) orderMatch.status = status;

//     // Order Increment ID filter
//     if (orderIncrementId) orderMatch.orderIncrementId = orderIncrementId;

//     // Reservation ID filter
//     if (reservationId) orderMatch.reservationId = reservationId;

//     // Created Date Range filter
//     const createdAtConditions = {};
//     if (createdDateFrom) createdAtConditions.$gte = new Date(createdDateFrom);
//     if (createdDateTo) createdAtConditions.$lte = new Date(createdDateTo);
//     if (Object.keys(createdAtConditions).length > 0)
//       orderMatch.createdAt = createdAtConditions;

//     // Release Date Range filter (only applicable for completed orders)
//     const releaseDateConditions = {};
//     if (releaseDateFrom) releaseDateConditions.$gte = new Date(releaseDateFrom);
//     if (releaseDateTo) releaseDateConditions.$lte = new Date(releaseDateTo);
//     if (Object.keys(releaseDateConditions).length > 0)
//       orderMatch.releaseDate = releaseDateConditions;

//     // Product Name filter (case-insensitive regex search)
//     if (productName) {
//       orderMatch["items.offerDetails.productName"] = {
//         $regex: productName,
//         $options: "i",
//       };
//     }

//     // Build the aggregation pipeline
//     const pipeline = [
//       { $unwind: "$items" },
//       {
//         $lookup: {
//           from: "offers",
//           localField: "items.offer",
//           foreignField: "_id",
//           as: "offerDetails",
//         },
//       },
//       { $unwind: "$offerDetails" },
//       {
//         $match: {
//           "offerDetails.seller": mongoose.Types.ObjectId(sellerId),
//         },
//       },
//       {
//         $lookup: {
//           from: "templates",
//           localField: "offerDetails.template",
//           foreignField: "_id",
//           as: "templateDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$templateDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $addFields: {
//           "offerDetails.coverImage": "$templateDetails.coverImage",
//           "offerDetails.productName": "$templateDetails.name", // Add product name
//         },
//       },
//       {
//         $group: {
//           _id: "$_id",
//           user: { $first: "$user" },
//           items: {
//             $push: {
//               offer: "$items.offer",
//               quantity: "$items.quantity",
//               price: "$items.price",
//               keys: "$items.keys",
//               _id: "$items._id",
//               offerDetails: "$offerDetails",
//             },
//           },
//           subtotal: { $first: "$subtotal" },
//           serviceFee: { $first: "$serviceFee" },
//           total: { $first: "$total" },
//           savings: { $first: "$savings" },
//           status: { $first: "$status" },
//           latest: { $first: "$latest" },
//           createdAt: { $first: "$createdAt" },
//           updatedAt: { $first: "$updatedAt" },
//           orderIncrementId: { $first: "$orderIncrementId" },
//           reservationId: { $first: "$reservationId" },
//           orderNumber: { $first: "$orderNumber" },
//           releaseDate: { $first: "$releaseDate" }, // Include releaseDate
//           __v: { $first: "$__v" },
//         },
//       },
//     ];

//     // Add dynamic $match for filters
//     if (Object.keys(orderMatch).length > 0) {
//       pipeline.push({ $match: orderMatch });
//     }

//     // Continue with user details and statistics
//     pipeline.push(
//       {
//         $lookup: {
//           from: "users",
//           localField: "user",
//           foreignField: "_id",
//           as: "userDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$userDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $project: {
//           user: 1,
//           items: 1,
//           subtotal: 1,
//           serviceFee: 1,
//           total: 1,
//           savings: 1,
//           status: 1,
//           latest: 1,
//           createdAt: 1,
//           updatedAt: 1,
//           orderIncrementId: 1,
//           reservationId: 1,
//           orderNumber: 1,
//           __v: 1,
//           releaseDate: 1,
//           userDetails: {
//             _id: 1,
//             name: 1,
//             email: 1,
//             username: 1,
//             createdAt: 1,
//             updatedAt: 1,
//           },
//         },
//       },
//       {
//         $group: {
//           _id: null,
//           totalOrders: { $sum: 1 },
//           completedOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
//           },
//           pendingOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
//           },
//           reviewOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "review"] }, 1, 0] },
//           },
//           refundOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "refund"] }, 1, 0] },
//           },
//           cancelledOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
//           },
//           orders: { $push: "$$ROOT" },
//         },
//       },
//       {
//         $project: {
//           _id: 0,
//           totalOrders: 1,
//           completedOrders: 1,
//           pendingOrders: 1,
//           reviewOrders: 1,
//           refundOrders: 1,
//           cancelledOrders: 1,
//           orders: 1,
//         },
//       }
//     );

//     const orders = await Order.aggregate(pipeline);
//     const result = orders[0] || {
//       totalOrders: 0,
//       completedOrders: 0,
//       pendingOrders: 0,
//       reviewOrders: 0,
//       refundOrders: 0,
//       cancelledOrders: 0,
//       orders: [],
//     };
//     res.status(200).json(result);
//   } catch (error) {
//     res
//       .status(500)
//       .json({ message: "Error fetching orders", error: error.message });
//   }
// };
// export const getSellerOrders = async (req, res) => {
//   const sellerId = req.user._id;
//   const {
//     productName,
//     orderIncrementId,
//     createdDateFrom,
//     createdDateTo,
//     releaseDateFrom,
//     releaseDateTo,
//     reservationId,
//     status,
//   } = req.query;

//   try {
//     const orderMatch = {};

//     // Status filter
//     if (status) orderMatch.status = status;

//     // Order Increment ID filter
//     if (orderIncrementId) orderMatch.orderIncrementId = orderIncrementId;

//     // Reservation ID filter
//     if (reservationId) orderMatch.reservationId = reservationId;

//     // Created Date Range
//     const createdAtConditions = {};
//     if (createdDateFrom) createdAtConditions.$gte = new Date(createdDateFrom);
//     if (createdDateTo) createdAtConditions.$lte = new Date(createdDateTo);
//     if (Object.keys(createdAtConditions).length > 0) {
//       orderMatch.createdAt = createdAtConditions;
//     }

//     // Release Date Range
//     const releaseDateConditions = {};
//     if (releaseDateFrom) releaseDateConditions.$gte = new Date(releaseDateFrom);
//     if (releaseDateTo) releaseDateConditions.$lte = new Date(releaseDateTo);
//     if (Object.keys(releaseDateConditions).length > 0) {
//       orderMatch.releaseDate = releaseDateConditions;
//     }

//     // Product Name filter (exact match)
//     if (productName) {
//       orderMatch["items.offerDetails.name"] = {
//         $regex: productName,
//         $options: "i",
//       };
//     }

//     const pipeline = [
//       { $unwind: "$items" },
//       {
//         $lookup: {
//           from: "offers",
//           localField: "items.offer",
//           foreignField: "_id",
//           as: "offerDetails",
//         },
//       },
//       { $unwind: "$offerDetails" },
//       {
//         $match: {
//           "offerDetails.seller": mongoose.Types.ObjectId(sellerId),
//         },
//       },
//       {
//         $lookup: {
//           from: "templates",
//           localField: "offerDetails.template",
//           foreignField: "_id",
//           as: "templateDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$templateDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $addFields: {
//           "offerDetails.coverImage": "$templateDetails.coverImage",
//         },
//       },
//       {
//         $group: {
//           _id: "$_id",
//           user: { $first: "$user" },
//           items: {
//             $push: {
//               offer: "$items.offer",
//               quantity: "$items.quantity",
//               price: "$items.price",
//               keys: "$items.keys",
//               _id: "$items._id",
//               offerDetails: "$offerDetails",
//             },
//           },
//           subtotal: { $first: "$subtotal" },
//           serviceFee: { $first: "$serviceFee" },
//           total: { $first: "$total" },
//           savings: { $first: "$savings" },
//           status: { $first: "$status" },
//           latest: { $first: "$latest" },
//           createdAt: { $first: "$createdAt" },
//           updatedAt: { $first: "$updatedAt" },
//           orderIncrementId: { $first: "$orderIncrementId" },
//           reservationId: { $first: "$reservationId" },
//           orderNumber: { $first: "$orderNumber" },
//           releaseDate: { $first: "$releaseDate" },
//           __v: { $first: "$__v" },
//         },
//       },
//     ];

//     if (Object.keys(orderMatch).length > 0) {
//       pipeline.push({ $match: orderMatch });
//     }

//     pipeline.push(
//       {
//         $lookup: {
//           from: "users",
//           localField: "user",
//           foreignField: "_id",
//           as: "userDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$userDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $project: {
//           user: 1,
//           items: 1,
//           subtotal: 1,
//           serviceFee: 1,
//           total: 1,
//           savings: 1,
//           status: 1,
//           latest: 1,
//           createdAt: 1,
//           updatedAt: 1,
//           orderIncrementId: 1,
//           reservationId: 1,
//           orderNumber: 1,
//           __v: 1,
//           releaseDate: 1,
//           userDetails: {
//             _id: 1,
//             name: 1,
//             email: 1,
//             username: 1,
//             createdAt: 1,
//             updatedAt: 1,
//           },
//         },
//       },
//       {
//         $group: {
//           _id: null,
//           totalOrders: { $sum: 1 },
//           completedOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
//           },
//           pendingOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
//           },
//           reviewOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "review"] }, 1, 0] },
//           },
//           refundOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "refund"] }, 1, 0] },
//           },
//           cancelledOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
//           },
//           orders: { $push: "$$ROOT" },
//         },
//       },
//       {
//         $project: {
//           _id: 0,
//           totalOrders: 1,
//           completedOrders: 1,
//           pendingOrders: 1,
//           reviewOrders: 1,
//           refundOrders: 1,
//           cancelledOrders: 1,
//           orders: 1,
//         },
//       }
//     );

//     const orders = await Order.aggregate(pipeline);
//     const result = orders[0] || {
//       totalOrders: 0,
//       completedOrders: 0,
//       pendingOrders: 0,
//       reviewOrders: 0,
//       refundOrders: 0,
//       cancelledOrders: 0,
//       orders: [],
//     };
//     res.status(200).json(result);
//   } catch (error) {
//     res
//       .status(500)
//       .json({ message: "Error fetching orders", error: error.message });
//   }
// };
export const getSellerOrders = async (req, res) => {
  const sellerId = req.user._id;
  const {
    productName,
    orderIncrementId,
    createdDateFrom,
    createdDateTo,
    releaseDateFrom,
    releaseDateTo,
    reservationId,
    status,
    page = 1,
    limit = 10,
  } = req.query;

  try {
    const orderMatch = {};
    const parsedPage = parseInt(page);
    const parsedLimit = parseInt(limit);
    const skip = (parsedPage - 1) * parsedLimit;

    // Status filter
    if (status) orderMatch.status = status;

    // Order Increment ID filter
    if (orderIncrementId) orderMatch.orderIncrementId = orderIncrementId;

    // Reservation ID filter
    if (reservationId) orderMatch.reservationId = reservationId;

    // Date range filters
    const handleDateFilter = (from, to, field) => {
      const conditions = {};
      if (from) conditions.$gte = new Date(from);
      if (to) conditions.$lte = new Date(to);
      if (Object.keys(conditions).length > 0) orderMatch[field] = conditions;
    };

    handleDateFilter(createdDateFrom, createdDateTo, "createdAt");
    handleDateFilter(releaseDateFrom, releaseDateTo, "releaseDate");

    // Product name filter (partial match, case-insensitive)
    if (productName) {
      orderMatch["items.offerDetails.name"] = {
        $regex: productName,
        $options: "i",
      };
    }

    const pipeline = [
      { $unwind: "$items" },
      {
        $lookup: {
          from: "offers",
          localField: "items.offer",
          foreignField: "_id",
          as: "offerDetails",
        },
      },
      { $unwind: "$offerDetails" },
      {
        $match: {
          "offerDetails.seller": mongoose.Types.ObjectId(sellerId),
        },
      },
      {
        $lookup: {
          from: "templates",
          localField: "offerDetails.template",
          foreignField: "_id",
          as: "templateDetails",
        },
      },
      {
        $unwind: {
          path: "$templateDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          "offerDetails.coverImage": "$templateDetails.coverImage",
        },
      },
      {
        $group: {
          _id: "$_id",
          user: { $first: "$user" },
          items: {
            $push: {
              offer: "$items.offer",
              quantity: "$items.quantity",
              price: "$items.price",
              keys: "$items.keys",
              _id: "$items._id",
              offerDetails: "$offerDetails",
              status: "$items.status",
            },
          },
          subtotal: { $first: "$subtotal" },
          serviceFee: { $first: "$serviceFee" },
          total: { $first: "$total" },
          savings: { $first: "$savings" },
          status: { $first: "$status" },
          latest: { $first: "$latest" },
          createdAt: { $first: "$createdAt" },
          updatedAt: { $first: "$updatedAt" },
          orderIncrementId: { $first: "$orderIncrementId" },
          reservationId: { $first: "$reservationId" },
          orderNumber: { $first: "$orderNumber" },
          releaseDate: { $first: "$releaseDate" },
          __v: { $first: "$__v" },
        },
      },
    ];

    if (Object.keys(orderMatch).length > 0) {
      pipeline.push({ $match: orderMatch });
    }

    // Add pagination and statistics using $facet
    pipeline.push(
      {
        $facet: {
          metadata: [
            {
              $group: {
                _id: null,
                totalOrders: { $sum: 1 },
                completedOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
                },
                pendingOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
                },
                reviewOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "review"] }, 1, 0] },
                },
                refundOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "refund"] }, 1, 0] },
                },
                cancelledOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
                },
              },
            },
          ],
          data: [
            { $skip: skip },
            { $limit: parsedLimit },
            {
              $lookup: {
                from: "users",
                localField: "user",
                foreignField: "_id",
                as: "userDetails",
              },
            },
            {
              $unwind: {
                path: "$userDetails",
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                user: 1,
                items: 1,
                subtotal: 1,
                serviceFee: 1,
                total: 1,
                savings: 1,
                status: 1,
                latest: 1,
                createdAt: 1,
                updatedAt: 1,
                orderIncrementId: 1,
                reservationId: 1,
                orderNumber: 1,
                __v: 1,
                releaseDate: 1,
                userDetails: {
                  _id: 1,
                  name: 1,
                  email: 1,
                  username: 1,
                  createdAt: 1,
                  updatedAt: 1,
                },
              },
            },
          ],
        },
      },
      {
        $unwind: "$metadata",
      },
      {
        $project: {
          totalOrders: "$metadata.totalOrders",
          completedOrders: "$metadata.completedOrders",
          pendingOrders: "$metadata.pendingOrders",
          reviewOrders: "$metadata.reviewOrders",
          refundOrders: "$metadata.refundOrders",
          cancelledOrders: "$metadata.cancelledOrders",
          orders: "$data",
          page: { $literal: parsedPage },
          limit: { $literal: parsedLimit },
          totalPages: {
            $ceil: {
              $divide: ["$metadata.totalOrders", parsedLimit],
            },
          },
        },
      }
    );

    const [result] = await Order.aggregate(pipeline);

    const finalResult = result || {
      totalOrders: 0,
      completedOrders: 0,
      pendingOrders: 0,
      reviewOrders: 0,
      refundOrders: 0,
      cancelledOrders: 0,
      orders: [],
      page: parsedPage,
      limit: parsedLimit,
      totalPages: 0,
    };

    res.status(200).json(finalResult);
  } catch (error) {
    res.status(500).json({
      message: "Error fetching orders",
      error: error.message,
    });
  }
};
export const updateOrderWithKeys = async (req, res) => {
  const userId = req.user._id;

  const { orderId } = req.params;
  const { offers: offerUpdates, action } = req.body; // `offers` array and action

  try {
    // Find the order and populate the offer details
    const order = await Order.findById(orderId).populate("items.offer");

    if (!order) {
      return res.status(404).json({ message: "Order not found" });
    }

    // Handle complete action
    if (action === "completed") {
      // Validate offer updates format
      if (!Array.isArray(offerUpdates)) {
        return res
          .status(400)
          .json({ message: "Invalid offer updates format" });
      }
      // Create a map for quick lookup of offer updates
      const offerUpdateMap = new Map();
      offerUpdates.forEach((update) => {
        offerUpdateMap.set(update.offerId.toString(), update);
      });

      // Process each item in the order
      for (const item of order.items) {
        const offer = item.offer;
        const offerUpdate = offerUpdateMap.get(offer._id.toString());

        if (!offerUpdate) {
          return res.status(400).json({
            message: `Update data missing for offer ${offer._id}`,
          });
        }

        if (offerUpdate.instantDelivery) {
          // Handle instant delivery
          if (offer.licenseKeys.length < item.quantity) {
            return res.status(400).json({
              message: `Order quanity is ${item.quantity} but only ${offer.licenseKeys.length} keys available for offer ${offer.name}. Please uplaod the stock and then complete the order`,
            });
          }

          // Transfer keys from offer to order item
          const keysToTransfer = offer.licenseKeys.splice(0, item.quantity);
          item.keys = keysToTransfer;

          // Update offer stock
          offer.stock = offer.licenseKeys.length;
          offer.sold += item.quantity;
        } else {
          // Handle manual delivery
          if (!offerUpdate.keys || offerUpdate.keys.length < item.quantity) {
            return res.status(400).json({
              message: `Insufficient keys provided for manual delivery offer ${offer._id}`,
            });
          }

          // Use provided keys for manual delivery
          item.keys = offerUpdate.keys.slice(0, item.quantity);
        }

        // Save offer changes if instant delivery
        if (offerUpdate.instantDelivery) {
          await offer.save();
        }
      }

      order.items.forEach((item) => {
        item.status = "completed";
      });

      await order.save();

      // Get user details for the email
      const user = await User.findById(order.user);

      // Check if all items are completed before sending email
      const allItemsCompleted = order.items.every(
        (item) => item.status === "completed"
      );

      if (allItemsCompleted) {
        // Send completion email
        const mailOptions = orderCompletionMail(order, user);
        mailOptions.to = user.email;
        mailOptions.from = process.env.EMAIL_FROM;

        try {
          await sendgrid.send(mailOptions);
        } catch (error) {
          console.error("Error sending order completion email:", error);
          // Don't throw error, continue with notifications
        }
      }

      //notify user
      notifyUser({
        userId: order.user,
        message: "Your recent order is completed.",
        title: "Order completed!",
        entityId: order._id,
        entityType: "order",
      }).finally();
      //notify the owner which is active user
      notifyUser({
        userId,
        message: "Your recent order is delivered.",
        title: "Order Delivered!",
        entityId: order._id,
        entityType: "order",
      }).finally();

      return res.status(200).json({
        message: "Order completed successfully",
        order,
      });
    }

    // Invalid action
    return res.status(400).json({
      message: "Invalid action. Use 'complete' or 'cancel'",
    });
  } catch (error) {
    res.status(500).json({
      message: "Error updating order",
      error: error.message,
    });
  }
};

// export const getUserOrders = async (req, res) => {
//   const userId = req.user._id; // Assuming the user ID is from the authenticated user

//   try {
//     const orders = await Order.aggregate([
//       // Step 1: Filter orders by the user's ID
//       {
//         $match: {
//           user: mongoose.Types.ObjectId(userId),
//         },
//       },
//       // Step 2: Unwind items to process each individually
//       {
//         $unwind: "$items",
//       },
//       // Lookup offer details for each item
//       {
//         $lookup: {
//           from: "offers",
//           localField: "items.offer",
//           foreignField: "_id",
//           as: "offerDetails",
//         },
//       },
//       {
//         $unwind: "$offerDetails",
//       },
//       // Lookup template details for the offer's template
//       {
//         $lookup: {
//           from: "templates",
//           localField: "offerDetails.template",
//           foreignField: "_id",
//           as: "templateDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$templateDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       // Add coverImage from template to offerDetails
//       {
//         $lookup: {
//           from: "users",
//           localField: "offerDetails.seller",
//           foreignField: "_id",
//           as: "sellerDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$sellerDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       {
//         $addFields: {
//           "offerDetails.coverImage": "$templateDetails.coverImage",
//           "offerDetails.sellerDetails": {
//             _id: "$sellerDetails._id",
//             name: "$sellerDetails.name",
//             email: "$sellerDetails.email",
//             role: "$sellerDetails.role",
//           },
//         },
//       },
//       // Step 3: Reconstruct the order with enriched items
//       {
//         $group: {
//           _id: "$_id",
//           user: { $first: "$user" },
//           items: {
//             $push: {
//               offer: "$items.offer",
//               quantity: "$items.quantity",
//               price: "$items.price",
//               keys: "$items.keys",
//               _id: "$items._id",
//               offerDetails: "$offerDetails",
//             },
//           },
//           subtotal: { $first: "$subtotal" },
//           serviceFee: { $first: "$serviceFee" },
//           total: { $first: "$total" },
//           savings: { $first: "$savings" },
//           status: { $first: "$status" },
//           latest: { $first: "$latest" },
//           createdAt: { $first: "$createdAt" },
//           updatedAt: { $first: "$updatedAt" },
//           orderIncrementId: { $first: "$orderIncrementId" },
//           reservationId: { $first: "$reservationId" },
//           orderNumber: { $first: "$orderNumber" },
//           __v: { $first: "$__v" },
//         },
//       },
//       // Lookup user details (buyer's info)
//       {
//         $lookup: {
//           from: "users",
//           localField: "user",
//           foreignField: "_id",
//           as: "userDetails",
//         },
//       },
//       {
//         $unwind: {
//           path: "$userDetails",
//           preserveNullAndEmptyArrays: true,
//         },
//       },
//       // Project necessary fields
//       {
//         $project: {
//           user: 1,
//           items: 1,
//           subtotal: 1,
//           serviceFee: 1,
//           total: 1,
//           savings: 1,
//           status: 1,
//           latest: 1,
//           createdAt: 1,
//           updatedAt: 1,
//           orderIncrementId: 1,
//           reservationId: 1,
//           orderNumber: 1,
//           __v: 1,
//           userDetails: {
//             _id: 1,
//             name: 1,
//             email: 1,
//             username: 1,
//             createdAt: 1,
//             updatedAt: 1,
//           },
//         },
//       },
//       // Calculate order statistics
//       {
//         $group: {
//           _id: null,
//           totalOrders: { $sum: 1 },
//           completedOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
//           },
//           pendingOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
//           },

//           reviewOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "review"] }, 1, 0] },
//           },
//           refundOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "refund"] }, 1, 0] },
//           },
//           cancelledOrders: {
//             $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
//           },
//           orders: { $push: "$$ROOT" },
//         },
//       },
//       // Final projection to clean up the output
//       {
//         $project: {
//           _id: 0,
//           totalOrders: 1,
//           completedOrders: 1,
//           pendingOrders: 1,
//           reviewOrders: 1,
//           refundOrders: 1,
//           cancelledOrders: 1,
//           orders: 1,
//         },
//       },
//     ]);

//     const result = orders[0] || {
//       totalOrders: 0,
//       completedOrders: 0,
//       pendingOrders: 0,
//       reviewOrders: 0,
//       refundOrders: 0,
//       cancelledOrders: 0,
//       orders: [],
//     };

//     res.status(200).json(result);
//   } catch (error) {
//     res
//       .status(500)
//       .json({ message: "Error fetching user orders", error: error.message });
//   }
// };

// export const getUserOrders = async (req, res) => {
//   const userId = req.user._id;
//   const {
//     orderNumber,
//     minPrice,
//     maxPrice,
//     status,
//     startDate,
//     endDate,
//     page = 1,
//     limit = 10,
//   } = req.query;

//   const parsedPage = parseInt(page);
//   const parsedLimit = parseInt(limit);
//   const skip = (parsedPage - 1) * parsedLimit;

//   // Build filter conditions
//   const matchConditions = { user: mongoose.Types.ObjectId(userId) };

//   if (orderNumber) {
//     matchConditions.orderNumber = { $regex: new RegExp(orderNumber, "i") };
//   }

//   if (minPrice || maxPrice) {
//     matchConditions.total = {};
//     if (minPrice) matchConditions.total.$gte = parseFloat(minPrice);
//     if (maxPrice) matchConditions.total.$lte = parseFloat(maxPrice);
//   }

//   if (status) {
//     const statuses = status.split(",");
//     matchConditions.status = { $in: statuses };
//   }

//   if (startDate || endDate) {
//     matchConditions.createdAt = {};
//     if (startDate) matchConditions.createdAt.$gte = new Date(startDate);
//     if (endDate) matchConditions.createdAt.$lte = new Date(endDate);
//   }

//   try {
//     const aggregationPipeline = [
//       { $match: matchConditions },
//       { $unwind: "$items" },
//       {
//         $lookup: {
//           from: "offers",
//           localField: "items.offer",
//           foreignField: "_id",
//           as: "offerDetails",
//         },
//       },
//       { $unwind: "$offerDetails" },
//       {
//         $lookup: {
//           from: "templates",
//           localField: "offerDetails.template",
//           foreignField: "_id",
//           as: "templateDetails",
//         },
//       },
//       {
//         $unwind: { path: "$templateDetails", preserveNullAndEmptyArrays: true },
//       },
//       {
//         $lookup: {
//           from: "users",
//           localField: "offerDetails.seller",
//           foreignField: "_id",
//           as: "sellerDetails",
//         },
//       },
//       { $unwind: { path: "$sellerDetails", preserveNullAndEmptyArrays: true } },
//       {
//         $addFields: {
//           "offerDetails.coverImage": "$templateDetails.coverImage",
//           "offerDetails.sellerDetails": {
//             _id: "$sellerDetails._id",
//             name: "$sellerDetails.name",
//             email: "$sellerDetails.email",
//             role: "$sellerDetails.role",
//           },
//         },
//       },
//       {
//         $group: {
//           _id: "$_id",
//           user: { $first: "$user" },
//           items: { $push: "$items" },
//           subtotal: { $first: "$subtotal" },
//           serviceFee: { $first: "$serviceFee" },
//           total: { $first: "$total" },
//           savings: { $first: "$savings" },
//           status: { $first: "$status" },
//           latest: { $first: "$latest" },
//           createdAt: { $first: "$createdAt" },
//           updatedAt: { $first: "$updatedAt" },
//           orderIncrementId: { $first: "$orderIncrementId" },
//           reservationId: { $first: "$reservationId" },
//           orderNumber: { $first: "$orderNumber" },
//           __v: { $first: "$__v" },
//         },
//       },
//       {
//         $lookup: {
//           from: "users",
//           localField: "user",
//           foreignField: "_id",
//           as: "userDetails",
//         },
//       },
//       { $unwind: { path: "$userDetails", preserveNullAndEmptyArrays: true } },
//       {
//         $project: {
//           user: 1,
//           items: 1,
//           subtotal: 1,
//           serviceFee: 1,
//           total: 1,
//           savings: 1,
//           status: 1,
//           latest: 1,
//           createdAt: 1,
//           updatedAt: 1,
//           orderIncrementId: 1,
//           reservationId: 1,
//           orderNumber: 1,
//           __v: 1,
//           userDetails: {
//             _id: 1,
//             name: 1,
//             email: 1,
//             username: 1,
//             createdAt: 1,
//             updatedAt: 1,
//           },
//         },
//       },
//       {
//         $facet: {
//           paginatedResults: [
//             { $sort: { createdAt: -1 } },
//             { $skip: skip },
//             { $limit: parsedLimit },
//           ],
//           totalCount: [
//             {
//               $group: {
//                 _id: null,
//                 totalOrders: { $sum: 1 },
//                 completedOrders: {
//                   $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
//                 },
//                 pendingOrders: {
//                   $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
//                 },
//                 reviewOrders: {
//                   $sum: { $cond: [{ $eq: ["$status", "review"] }, 1, 0] },
//                 },
//                 refundOrders: {
//                   $sum: { $cond: [{ $eq: ["$status", "refund"] }, 1, 0] },
//                 },
//                 cancelledOrders: {
//                   $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
//                 },
//               },
//             },
//           ],
//         },
//       },
//       {
//         $project: {
//           orders: "$paginatedResults",
//           totalOrders: { $arrayElemAt: ["$totalCount.totalOrders", 0] },
//           completedOrders: { $arrayElemAt: ["$totalCount.completedOrders", 0] },
//           pendingOrders: { $arrayElemAt: ["$totalCount.pendingOrders", 0] },
//           reviewOrders: { $arrayElemAt: ["$totalCount.reviewOrders", 0] },
//           refundOrders: { $arrayElemAt: ["$totalCount.refundOrders", 0] },
//           cancelledOrders: { $arrayElemAt: ["$totalCount.cancelledOrders", 0] },
//           currentPage: { $literal: parsedPage },
//           totalPages: {
//             $ceil: {
//               $divide: [
//                 { $arrayElemAt: ["$totalCount.totalOrders", 0] },
//                 parsedLimit,
//               ],
//             },
//           },
//         },
//       },
//     ];

//     const [result] = await Order.aggregate(aggregationPipeline);

//     const finalResult = {
//       totalOrders: result?.totalOrders || 0,
//       completedOrders: result?.completedOrders || 0,
//       pendingOrders: result?.pendingOrders || 0,
//       reviewOrders: result?.reviewOrders || 0,
//       refundOrders: result?.refundOrders || 0,
//       cancelledOrders: result?.cancelledOrders || 0,
//       currentPage: parsedPage,
//       totalPages: result?.totalPages || 0,
//       orders: result?.orders || [],
//     };

//     res.status(200).json(finalResult);
//   } catch (error) {
//     res.status(500).json({
//       message: "Error fetching user orders",
//       error: error.message,
//     });
//   }
// };
export const getUserOrders = async (req, res) => {
  const userId = req.user._id;
  const { orderId, createdAt, price, page = 1, limit = 10 } = req.query;

  try {
    // Parse pagination parameters
    const parsedPage = Math.max(1, parseInt(page)) || 1;
    const parsedLimit = Math.max(1, parseInt(limit)) || 10;
    const skip = (parsedPage - 1) * parsedLimit;

    // Build filter conditions
    const matchConditions = {
      user: mongoose.Types.ObjectId(userId),
    };

    // Order ID filter (partial match on orderNumber)
    if (orderId) {
      matchConditions.orderNumber = {
        $regex: orderId,
        $options: "i",
      };
    }

    // Date filter (exact day)
    if (createdAt) {
      const startDate = new Date(createdAt);
      startDate.setHours(0, 0, 0, 0);
      const endDate = new Date(createdAt);
      endDate.setHours(23, 59, 59, 999);
      matchConditions.createdAt = {
        $gte: startDate,
        $lte: endDate,
      };
    }

    // Price range filter
    if (price) {
      matchConditions.total = {};
      if (price.gt) matchConditions.total.$gt = parseFloat(price.gt);
      if (price.lte) matchConditions.total.$lte = parseFloat(price.lte);
    }

    const aggregationPipeline = [
      // Initial match with all filters
      { $match: matchConditions },
      // Existing pipeline stages
      { $unwind: "$items" },
      {
        $lookup: {
          from: "offers",
          localField: "items.offer",
          foreignField: "_id",
          as: "offerDetails",
        },
      },
      { $unwind: "$offerDetails" },
      {
        $lookup: {
          from: "templates",
          localField: "offerDetails.template",
          foreignField: "_id",
          as: "templateDetails",
        },
      },
      {
        $unwind: {
          path: "$templateDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "offerDetails.seller",
          foreignField: "_id",
          as: "sellerDetails",
        },
      },
      {
        $unwind: {
          path: "$sellerDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $addFields: {
          "offerDetails.coverImage": "$templateDetails.coverImage",
          "offerDetails.sellerDetails": {
            _id: "$sellerDetails._id",
            name: "$sellerDetails.name",
            email: "$sellerDetails.email",
            role: "$sellerDetails.role",
          },
        },
      },
      {
        $group: {
          _id: "$_id",
          user: { $first: "$user" },
          items: {
            $push: {
              offer: "$items.offer",
              quantity: "$items.quantity",
              price: "$items.price",
              keys: "$items.keys",
              _id: "$items._id",
              status: "$items.status",
              offerDetails: "$offerDetails",
            },
          },
          subtotal: { $first: "$subtotal" },
          serviceFee: { $first: "$serviceFee" },
          total: { $first: "$total" },
          savings: { $first: "$savings" },
          status: { $first: "$status" },
          latest: { $first: "$latest" },
          createdAt: { $first: "$createdAt" },
          updatedAt: { $first: "$updatedAt" },
          orderIncrementId: { $first: "$orderIncrementId" },
          reservationId: { $first: "$reservationId" },
          orderNumber: { $first: "$orderNumber" },
          __v: { $first: "$__v" },
        },
      },
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails",
        },
      },
      {
        $unwind: {
          path: "$userDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          user: 1,
          items: 1,
          subtotal: 1,
          serviceFee: 1,
          total: 1,
          savings: 1,
          status: 1,
          latest: 1,
          createdAt: 1,
          updatedAt: 1,
          orderIncrementId: 1,
          reservationId: 1,
          orderNumber: 1,
          __v: 1,
          userDetails: {
            _id: 1,
            name: 1,
            email: 1,
            username: 1,
            createdAt: 1,
            updatedAt: 1,
          },
        },
      },
      // Add pagination and statistics
      {
        $facet: {
          metadata: [
            {
              $group: {
                _id: null,
                totalOrders: { $sum: 1 },
                completedOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "completed"] }, 1, 0] },
                },
                pendingOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "pending"] }, 1, 0] },
                },
                reviewOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "review"] }, 1, 0] },
                },
                refundOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "refund"] }, 1, 0] },
                },
                cancelledOrders: {
                  $sum: { $cond: [{ $eq: ["$status", "cancelled"] }, 1, 0] },
                },
              },
            },
          ],
          data: [
            { $sort: { createdAt: -1 } },
            { $skip: skip },
            { $limit: parsedLimit },
          ],
        },
      },
      {
        $unwind: "$metadata",
      },
      {
        $project: {
          totalOrders: "$metadata.totalOrders",
          completedOrders: "$metadata.completedOrders",
          pendingOrders: "$metadata.pendingOrders",
          reviewOrders: "$metadata.reviewOrders",
          refundOrders: "$metadata.refundOrders",
          cancelledOrders: "$metadata.cancelledOrders",
          orders: "$data",
          currentPage: { $literal: parsedPage },
          totalPages: {
            $ceil: { $divide: ["$metadata.totalOrders", parsedLimit] },
          },
        },
      },
    ];

    const [result] = await Order.aggregate(aggregationPipeline);

    const finalResult = result || {
      totalOrders: 0,
      completedOrders: 0,
      pendingOrders: 0,
      reviewOrders: 0,
      refundOrders: 0,
      cancelledOrders: 0,
      orders: [],
      currentPage: parsedPage,
      totalPages: 0,
    };

    res.status(200).json(finalResult);
  } catch (error) {
    res.status(500).json({
      message: "Error fetching user orders",
      error: error.message,
    });
  }
};

export const addReviewToOffer = async (req, res) => {
  const { offerId } = req.params;
  const { stars, content, orderNumber } = req.body;
  const userId = req.user._id; // Authenticated user's ID

  try {
    // Validate input
    if (!stars || stars < 1 || stars > 5) {
      return res.status(400).json({ message: "Stars must be between 1 and 5" });
    }
    if (!content || content.trim().length === 0) {
      return res.status(400).json({ message: "Review content is required" });
    }

    // Find the offer
    const offer = await Offer.findOne({
      _id: mongoose.Types.ObjectId(offerId),
    });
    if (!offer) {
      return res.status(404).json({ message: "Offer not found" });
    }
    const reviewId = offer.ratings.length + 1;

    // Check if the user has already reviewed this offer
    const existingReview = offer.ratings.find(
      (rating) => rating.user.toString() === userId.toString()
    );
    if (existingReview) {
      return res
        .status(400)
        .json({ message: "You have already reviewed this offer" });
    }

    // Fetch the user's avatar and seller's details
    const user = await User.findById(userId).select("avatar name");
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const seller = await User.findById(offer.seller).select("avatar name");
    if (!seller) {
      return res.status(404).json({ message: "Seller not found" });
    }

    // Create a new review with user and seller details
    const newReview = {
      reviewId,
      user: userId,
      userAvatar: user.avatar, // Add user's avatar
      offerId: offerId, // Include the offer ID
      userName: user.name, // Add user's name
      stars,
      content,
      helpful: 0, // Default helpful count
      notHelpful: 0, // Default not helpful count
      sellerResponse: "", // Default empty seller response
      sellerAvatar: seller.avatar, // Add seller's avatar
      sellerName: seller.name, // Add seller's name
      createdAt: new Date(),
      orderNumber: orderNumber,
    };

    // Add the review to the offer
    offer.ratings.push(newReview);
    await offer.save();

    //for buyer
    notifyUser({
      userId,
      message: "Your review has been added successfully",
      title: "Review submitted!",
      entityId: offer._id,
      entityType: "review",
    }).finally();

    //for seller
    notifyUser({
      userId: offer.seller,
      message: "Buyer left a review",
      title: "Review Added!",
      entityId: offer._id,
      entityType: "review",
    }).finally();

    res.status(201).json({
      message: "Review added successfully",
      review: newReview,
    });
  } catch (error) {
    res
      .status(500)
      .json({ message: "Error adding review", error: error.message });
  }
};

// export const getRatingsBySeller = async (req, res) => {
//   const { sellerId } = req.params;

//   try {
//     // Find all offers created by the seller
//     const offers = await Offer.find({
//       seller: mongoose.Types.ObjectId(sellerId),
//     });

//     if (!offers || offers.length === 0) {
//       return res
//         .status(404)
//         .json({ message: "No offers found for this seller" });
//     }

//     // Extract all ratings from the offers
//     const ratings = offers.flatMap((offer) =>
//       offer.ratings.map((rating) => ({
//         ...rating.toObject(),
//         offerId: offer._id, // Include the offer ID for context
//         offerName: offer.name, // Include the offer name for context
//       }))
//     );

//     if (ratings.length === 0) {
//       return res
//         .status(404)
//         .json({ message: "No ratings found for this seller" });
//     }

//     // Calculate the average rating
//     const totalStars = ratings.reduce((sum, rating) => sum + rating.stars, 0);
//     const averageRating = totalStars / ratings.length;

//     res.status(200).json({
//       message: "Ratings fetched successfully",
//       totalRatings: ratings.length,
//       averageRating: parseFloat(averageRating.toFixed(2)), // Round to 2 decimal places
//       ratings,
//     });
//   } catch (error) {
//     res
//       .status(500)
//       .json({ message: "Error fetching ratings", error: error.message });
//   }
// };

export const getRatingsBySeller = async (req, res) => {
  const { sellerId } = req.params;
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;

  if (page < 1 || limit < 1) {
    return res.status(400).json({ message: "Invalid page or limit value" });
  }

  try {
    // Aggregation pipeline for ratings with pagination
    const aggregationResult = await Offer.aggregate([
      { $match: { seller: mongoose.Types.ObjectId(sellerId) } },
      { $unwind: "$ratings" },
      {
        $facet: {
          metadata: [
            {
              $group: {
                _id: null,
                totalRatings: { $sum: 1 },
                averageStars: { $avg: "$ratings.stars" },
              },
            },
          ],
          data: [
            { $skip: (page - 1) * limit },
            { $limit: limit },
            {
              $project: {
                stars: "$ratings.stars",
                orderNumber: "$ratings.orderNumber",
                comment: "$ratings.content",
                createdAt: "$ratings.createdAt",
                updatedAt: "$ratings.updatedAt",
                user: "$ratings.userName",
                userAvatar: "$ratings.userAvatar",
                sellerResponse: "$ratings.sellerResponse",
                sellerAvatar: "$ratings.sellerAvatar",
                sellerName: "$ratings.sellerName",
                helpful: "$ratings.helpful",
                notHelpful: "$ratings.notHelpful",
                offerId: "$_id",
                offerName: "$name",
              },
            },
          ],
        },
      },
    ]);

    // Extract aggregation results
    const result = aggregationResult[0] || { metadata: [], data: [] };
    const metadata = result.metadata[0] || { totalRatings: 0, averageStars: 0 };
    const totalRatings = metadata.totalRatings;
    const averageRating = metadata.averageStars || 0;

    // Get shop followers count
    const shop = await Shop.findOne({ seller: sellerId });
    const followersCount = shop?.followers?.length || 0;

    res.status(200).json({
      message: "Ratings fetched successfully",
      totalRatings: totalRatings,
      averageRating: parseFloat(averageRating.toFixed(2)),
      followersCount,
      currentPage: page,
      totalPages: Math.ceil(totalRatings / limit),
      ratings: result.data,
    });
  } catch (error) {
    res.status(500).json({
      message: "Error fetching ratings",
      error: error.message,
    });
  }
};
export const respondToReview = async (req, res) => {
  const { offerId, reviewId } = req.params;
  const { sellerResponse } = req.body;

  try {
    // Find the offer by offerId
    const offer = await Offer.findById(offerId);

    if (!offer) {
      return res.status(404).json({ message: "Offer not found" });
    }

    // Find the specific rating within the offer's ratings array
    const rating = offer.ratings.find(
      (rating) => rating.reviewId === parseInt(reviewId)
    );

    if (!rating) {
      return res.status(404).json({ message: "Review not found" });
    }

    // Update the seller response and optional fields
    rating.sellerResponse = sellerResponse;

    // Save the updated offer
    await offer.save();

    res.status(200).json({
      message: "Seller response updated successfully",
      rating,
    });
  } catch (error) {
    res.status(500).json({
      message: "Error updating seller response",
      error: error.message,
    });
  }
};

export const kyc = async (req, res) => {};

// Update order status - Admin/Management API
export const updateOrderStatus = asyncHandler(async (req, res) => {
  const { orderId } = req.params;
  const { status } = req.body;

  try {
    // Validate required fields
    if (!status) {
      return res.status(400).json({
        success: false,
        message: "Status is required"
      });
    }

    // Validate status value against allowed enum values
    const allowedStatuses = ["pending", "review", "completed", "refund", "cancelled", "delivered"];
    if (!allowedStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: `Invalid status. Allowed values are: ${allowedStatuses.join(", ")}`
      });
    }

    // Find and update the order
    const order = await Order.findById(orderId);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: "Order not found"
      });
    }

    // Update the order status
    order.status = status;
    order.updatedAt = new Date();

    // Save the updated order
    const updatedOrder = await order.save();

    // Populate user details for response
    await updatedOrder.populate({
      path: 'user',
      select: 'firstName lastName email role avatar'
    });

    // Populate item details for response
    await updatedOrder.populate({
      path: 'items.offer',
      select: 'name category subcategory region expectedPrice customerPays',
      populate: {
        path: 'seller',
        select: 'firstName lastName email avatar'
      }
    });

    res.status(200).json({
      success: true,
      message: "Order status updated successfully",
      data: {
        order: updatedOrder
      }
    });

  } catch (error) {
    console.error('Error in updateOrderStatus:', error);
    res.status(500).json({
      success: false,
      message: "Error updating order status",
      error: error.message
    });
  }
});

// Get all orders - Admin/Management API for frontend integration
export const getAllOrders = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    status,
    orderNumber,
    orderIncrementId,
    reservationId,
    userId,
    sellerId,
    createdDateFrom,
    createdDateTo,
    minTotal,
    maxTotal,
    sortBy = "createdAt",
    sortOrder = "desc",
    search,
  } = req.query;

  try {
    // Parse pagination parameters
    const parsedPage = Math.max(1, parseInt(page));
    const parsedLimit = Math.max(1, Math.min(100, parseInt(limit))); // Max 100 items per page
    const skip = (parsedPage - 1) * parsedLimit;

    // Build match conditions
    const matchConditions = {};

    // Status filter
    if (status) {
      matchConditions.status = status;
    }

    // Order number filters
    if (orderNumber) {
      matchConditions.orderNumber = { $regex: orderNumber, $options: "i" };
    }
    if (orderIncrementId) {
      matchConditions.orderIncrementId = {
        $regex: orderIncrementId,
        $options: "i",
      };
    }
    if (reservationId) {
      matchConditions.reservationId = { $regex: reservationId, $options: "i" };
    }

    // User filter
    if (userId) {
      matchConditions.user = new mongoose.Types.ObjectId(userId);
    }

    // Date range filter
    if (createdDateFrom || createdDateTo) {
      matchConditions.createdAt = {};
      if (createdDateFrom) {
        matchConditions.createdAt.$gte = new Date(createdDateFrom);
      }
      if (createdDateTo) {
        matchConditions.createdAt.$lte = new Date(createdDateTo);
      }
    }

    // Total amount range filter
    if (minTotal || maxTotal) {
      matchConditions.total = {};
      if (minTotal) {
        matchConditions.total.$gte = parseFloat(minTotal);
      }
      if (maxTotal) {
        matchConditions.total.$lte = parseFloat(maxTotal);
      }
    }

    // Build aggregation pipeline
    const pipeline = [
      // Match orders based on filters
      { $match: matchConditions },

      // Lookup user details
      {
        $lookup: {
          from: "users",
          localField: "user",
          foreignField: "_id",
          as: "userDetails",
          pipeline: [
            {
              $project: {
                _id: 1,
                firstName: 1,
                lastName: 1,
                name: 1,
                email: 1,
                role: 1,
                avatar: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$userDetails",
          preserveNullAndEmptyArrays: true,
        },
      },

      // Unwind items to process each item separately
      { $unwind: "$items" },

      // Lookup offer details for each item
      {
        $lookup: {
          from: "offers",
          localField: "items.offer",
          foreignField: "_id",
          as: "offerDetails",
          pipeline: [
            {
              $lookup: {
                from: "templates",
                localField: "template",
                foreignField: "_id",
                as: "templateDetails",
                pipeline: [
                  {
                    $project: {
                      templateName: 1,
                      coverImage: 1,
                      category: 1,
                    },
                  },
                ],
              },
            },
            {
              $lookup: {
                from: "users",
                localField: "seller",
                foreignField: "_id",
                as: "sellerDetails",
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      firstName: 1,
                      lastName: 1,
                      email: 1,
                      avatar: 1,
                    },
                  },
                ],
              },
            },
            {
              $addFields: {
                templateDetails: { $arrayElemAt: ["$templateDetails", 0] },
                sellerDetails: { $arrayElemAt: ["$sellerDetails", 0] },
              },
            },
            {
              $project: {
                _id: 1,
                name: 1,
                category: 1,
                subcategory: 1,
                region: 1,
                expectedPrice: 1,
                customerPays: 1,
                instantDelivery: 1,
                active: 1,
                templateDetails: 1,
                sellerDetails: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: "$offerDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
    ];

    // Add seller filter if provided (after lookup)
    if (sellerId) {
      pipeline.push({
        $match: {
          "offerDetails.sellerDetails._id": new mongoose.Types.ObjectId(
            sellerId
          ),
        },
      });
    }

    // Add search functionality
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { orderNumber: { $regex: search, $options: "i" } },
            { orderIncrementId: { $regex: search, $options: "i" } },
            { reservationId: { $regex: search, $options: "i" } },
            { "userDetails.firstName": { $regex: search, $options: "i" } },
            { "userDetails.lastName": { $regex: search, $options: "i" } },
            { "userDetails.email": { $regex: search, $options: "i" } },
            { "offerDetails.name": { $regex: search, $options: "i" } },
            {
              "offerDetails.templateDetails.templateName": {
                $regex: search,
                $options: "i",
              },
            },
          ],
        },
      });
    }

    // Group back the items per order
    pipeline.push({
      $group: {
        _id: "$_id",
        orderNumber: { $first: "$orderNumber" },
        orderIncrementId: { $first: "$orderIncrementId" },
        reservationId: { $first: "$reservationId" },
        user: { $first: "$user" },
        userDetails: { $first: "$userDetails" },
        items: {
          $push: {
            _id: "$items._id",
            offer: "$items.offer",
            quantity: "$items.quantity",
            price: "$items.price",
            status: "$items.status",
            keys: "$items.keys",
            offerDetails: "$offerDetails",
          },
        },
        subtotal: { $first: "$subtotal" },
        serviceFee: { $first: "$serviceFee" },
        total: { $first: "$total" },
        savings: { $first: "$savings" },
        status: { $first: "$status" },
        latest: { $first: "$latest" },
        releaseDate: { $first: "$releaseDate" },
        paymentData: { $first: "$paymentData" },
        createdAt: { $first: "$createdAt" },
        updatedAt: { $first: "$updatedAt" },
      },
    });

    // Add computed fields
    pipeline.push({
      $addFields: {
        totalItems: { $size: "$items" },
        customerName: {
          $concat: [
            { $ifNull: ["$userDetails.firstName", ""] },
            " ",
            { $ifNull: ["$userDetails.lastName", ""] },
          ],
        },
        uniqueSellers: {
          $size: {
            $setUnion: ["$items.offerDetails.sellerDetails._id"],
          },
        },
      },
    });

    // Create facet for pagination and statistics
    pipeline.push({
      $facet: {
        // Get paginated data
        data: [
          // Sort
          {
            $sort: {
              [sortBy]: sortOrder === "desc" ? -1 : 1,
            },
          },
          { $skip: skip },
          { $limit: parsedLimit },
        ],
        // Get statistics
        metadata: [
          {
            $group: {
              _id: null,
              totalOrders: { $sum: 1 },
              totalRevenue: { $sum: "$total" },
              averageOrderValue: { $avg: "$total" },
              statusCounts: {
                $push: "$status",
              },
            },
          },
          {
            $addFields: {
              statusBreakdown: {
                $reduce: {
                  input: "$statusCounts",
                  initialValue: {},
                  in: {
                    $mergeObjects: [
                      "$$value",
                      {
                        $arrayToObject: [
                          [
                            {
                              k: "$$this",
                              v: {
                                $add: [
                                  {
                                    $ifNull: [
                                      {
                                        $getField: {
                                          field: "$$this",
                                          input: "$$value",
                                        },
                                      },
                                      0,
                                    ],
                                  },
                                  1,
                                ],
                              },
                            },
                          ],
                        ],
                      },
                    ],
                  },
                },
              },
            },
          },
        ],
      },
    });

    // Final projection
    pipeline.push({
      $project: {
        orders: "$data",
        totalOrders: { $arrayElemAt: ["$metadata.totalOrders", 0] },
        totalRevenue: { $arrayElemAt: ["$metadata.totalRevenue", 0] },
        averageOrderValue: { $arrayElemAt: ["$metadata.averageOrderValue", 0] },
        statusBreakdown: { $arrayElemAt: ["$metadata.statusBreakdown", 0] },
        currentPage: { $literal: parsedPage },
        totalPages: {
          $ceil: {
            $divide: [
              { $arrayElemAt: ["$metadata.totalOrders", 0] },
              parsedLimit,
            ],
          },
        },
        hasNextPage: {
          $gt: [
            { $arrayElemAt: ["$metadata.totalOrders", 0] },
            { $multiply: [parsedPage, parsedLimit] },
          ],
        },
        hasPrevPage: { $gt: [parsedPage, 1] },
      },
    });

    // Execute aggregation
    const [result] = await Order.aggregate(pipeline);

    // Format response
    const response = {
      success: true,
      data: {
        orders: result?.orders || [],
        pagination: {
          currentPage: parsedPage,
          totalPages: result?.totalPages || 0,
          totalOrders: result?.totalOrders || 0,
          hasNextPage: result?.hasNextPage || false,
          hasPrevPage: result?.hasPrevPage || false,
          limit: parsedLimit,
        },
        statistics: {
          totalRevenue: result?.totalRevenue || 0,
          averageOrderValue: result?.averageOrderValue || 0,
          statusBreakdown: result?.statusBreakdown || {},
        },
        filters: {
          status,
          orderNumber,
          orderIncrementId,
          reservationId,
          userId,
          sellerId,
          createdDateFrom,
          createdDateTo,
          minTotal,
          maxTotal,
          search,
        },
        sorting: {
          sortBy,
          sortOrder,
        },
      },
    };

    res.status(200).json(response);
  } catch (error) {
    console.error("Error in getAllOrders:", error);
    res.status(500).json({
      success: false,
      message: "Error fetching all orders",
      error: error.message,
    });
  }
});

// Route (remove protect/seller middleware)

// Controller
// export const getSellerTopOffers = async (req, res) => {
//   const { sellerId } = req.params;
//   const limit = parseInt(req.query.limit) || 10;

//   try {
//     const pipeline = [
//       { $unwind: "$items" },
//       {
//         $lookup: {
//           from: "offers",
//           localField: "items.offer",
//           foreignField: "_id",
//           as: "offerDetails",
//         },
//       },
//       { $unwind: "$offerDetails" },
//       {
//         $match: {
//           "offerDetails.seller": mongoose.Types.ObjectId(sellerId),
//           "offerDetails.status": "active", // Only count active offers
//         },
//       },
//       {
//         $group: {
//           _id: "$offerDetails._id",
//           totalSold: { $sum: "$items.quantity" },
//           totalRevenue: {
//             $sum: { $multiply: ["$items.quantity", "$items.price"] },
//           },
//           offer: { $first: "$offerDetails" },
//         },
//       },
//       {
//         $lookup: {
//           from: "templates",
//           localField: "offer.template",
//           foreignField: "_id",
//           as: "templateDetails",
//         },
//       },
//       {
//         $unwind: { path: "$templateDetails", preserveNullAndEmptyArrays: true },
//       },
//       {
//         $addFields: {
//           "offer.coverImage": "$templateDetails.coverImage",
//         },
//       },
//       {
//         $project: {
//           _id: 0,
//           offer: 1,
//           totalSold: 1,
//           totalRevenue: 1,
//         },
//       },
//       { $sort: { totalSold: -1 } },
//       { $limit: limit },
//     ];

//     const topOffers = await Order.aggregate(pipeline);

//     if (topOffers.length === 0) {
//       return res.status(404).json({
//         message: "No offers found for this seller",
//       });
//     }

//     res.status(200).json({
//       message: "Top offers fetched successfully",
//       sellerId,
//       totalOffers: topOffers.length,
//       topOffers,
//     });
//   } catch (error) {
//     res.status(500).json({
//       message: "Error fetching top offers",
//       error: error.message,
//     });
//   }
// };
