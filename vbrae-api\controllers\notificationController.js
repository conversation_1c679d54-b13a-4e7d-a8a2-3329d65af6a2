import NotificationModel from "../models/notificationModel.js";
import Notification from "../models/notificationModel.js";
import Order from "../models/OrderModel.js";
import Offer from "../models/offerModel.js";
import Template from "../models/templateModel.js";
import Conversation from "../models/conversationModel.js";
import User from "../models/userModel.js";
import TicketModel from "../models/ticketModel.js";

export const getAllNotifications = async (req, res) => {
  try {
    const userId = req.user._id;
    const limit = req.query.limit;

    const notifications = await NotificationModel.find({ userId }).sort({ createdAt: -1 }).limit(limit);

    const populated = await Promise.all(
      notifications.map(async (n) => {
        let entity = null;

        if (n.entityType === 'order') {
          const order = await Order.findById(n.entityId).limit(5);

          if (order && order.items && order.items.length > 0) {
            const offerId = order.items[0].offer;

            const offer = await Offer.findById(offerId);

            const template = await Template.findById(offer.template);

            entity = {
              _id: offer._id,
              templateCoverImage: template?.coverImage || null
            };
          }
        } else if (n.entityType === 'review') {
          const offer = await Offer.findById(n.entityId);
          const template = await Template.findById(offer.template);

            entity = {
              _id: offer._id,
              templateCoverImage: template?.coverImage || null
            };
        }

        else if(n.entityType === 'conversation-offer'){
            const conversation = await Conversation.findById(n.entityId);
            const user = await User.findById(n.senderId); // senderId

            entity = {
                _id: conversation._id,
                templateCoverImage: user.avatar,
            };
        }

        else if(n.entityType === 'conversation-ticket'){
            const ticket = await TicketModel.findById(n.entityId);
            const user = await User.findById(n.senderId); // senderId

            entity = {
                _id: ticket.conversationId,
                templateCoverImage: user.avatar,
            };
        }

        return {
          ...n.toObject(),
          entity,
        };
      })
    );

    res.status(200).json({ data: populated });
  } catch (err) {
    console.error('Failed to fetch notifications:', err);
    res.status(500).json({ message: 'Internal Server Error' });
  }
};

export const patchNotification = async (request, response) => {
    const userId = request.user._id;
    try {
        await NotificationModel.updateMany(
            { userId, isRead: false },
            { $set: { isRead: true } }
        );
        response.status(200).json({ status: "success", message: "Notifications updated", userId });
    }
    catch (error) {
        console.error('Failed to patch notifications:', error);
        response.status(500).json({ message: 'Internal Server Error' });
    }
}

export const postNotification = async (request, response) => {

    const userId = request.user._id;
    const {title, message, entityId, senderId, entityType} = request.body;

    try {
        const r = await Notification.create({
            userId, //receiverId
            title,
            message,
            entityType,
            entityId,
            senderId,
        });
        response.status(200).json({ status: "success", message: "Notifications added", data:r });
    }
    catch (error) {
        console.error('Failed to patch notifications:', error);
        response.status(500).json({ message: 'Internal Server Error' });
    }
}

export const deleteNotification = async (request, response) => {

    const notificationId = request.params._id;

    try {
        await Notification.deleteOne({_id: notificationId});
        response.status(200).json({ status: "success", message: "Notifications deleted" });
    }
    catch (error) {
        console.error('Failed to patch notifications:', error);
        response.status(500).json({ message: 'Internal Server Error' });
    }
}