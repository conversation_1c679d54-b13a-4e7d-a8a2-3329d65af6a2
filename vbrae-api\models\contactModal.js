import mongoose from "mongoose";

const contactSchema = new mongoose.Schema(
    {
        email: {
            type: String,
            required: true,
            trim: true,
            lowercase: true,
        },
        description: {
            type: String,
            required: true,
            trim: true
        },
        attachments: {
            type: [String],
            required: true,
            default: []
        },
    },
    { timestamps: true }
);

export default mongoose.model("Contact", contactSchema);
