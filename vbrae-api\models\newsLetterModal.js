import mongoose from "mongoose";

const newsLetterSchema = new mongoose.Schema(
  {
    email: {
        type: String,
        required: true,
        trim: true,
        lowercase: true,
        unique: true
    },
    country: {
        type: String,
        default: null,
        required: false,
    }
  },
  { timestamps: true }
);

const NewsletterModel = mongoose.model("Newsletter", newsLetterSchema);

export default NewsletterModel;
