import mongoose from "mongoose";

const RequestSchema = new mongoose.Schema(
  {
    title: { type: String, required: true },
    region: { type: String, required: true },
    category: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "SubCategory",
      default: null,
    },
    // subCategory: { type: String,  required: true},
    language: [{ type: String }],
    additionalInformation: { type: String },
    isApproved: { type: String, default: "waiting" },
    createdAt: { type: Date, default: Date.now },
    seller: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
  },
  { timestamps: true }
);

const Request = mongoose.model("Request", RequestSchema);
export default Request;
