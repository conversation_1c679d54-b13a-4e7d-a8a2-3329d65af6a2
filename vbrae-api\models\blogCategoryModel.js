import mongoose from "mongoose";

const blogCategorySchema = new mongoose.Schema(
  {
    language: {
      type: String,
      required: true,
    },
    categoryName: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    slug: {
      type: String,
      unique: true,
    },
    description: {
      type: String,
    },
    keywords: {
      type: [String],
    },
    order: {
      type: Number,
      default: 0,
    },
  },
  { timestamps: true }
);

blogCategorySchema.pre("save", function (next) {
  if (!this.slug) {
    this.slug = this.categoryName.toLowerCase().replace(/ /g, "-");
  }
  next();
});

const BlogCategory = mongoose.model("BlogCategory", blogCategorySchema);
export default BlogCategory;
