import express from "express";
import {
  createBlogCategory,
  getBlogCategory,
  updateBlogCategory,
  deleteBlogCategory,
  getAllBlogCategories,
} from "./../controllers/blogCategoryController.js";
import { protect, admin } from "../middleware/authMiddleware.js";

const router = express.Router();

router
  .route("/")
  .post(protect, admin, createBlogCategory)
  .get(getAllBlogCategories);

router
  .route("/:id")
  .get(getBlogCategory)
  .patch(protect, admin, updateBlogCategory)
  .delete(protect, admin, deleteBlogCategory);

export default router;
