import Template from "../models/templateModel.js";
import * as factory from "./handlerFactory.js";

import slugify from "slugify";

// Create a new template
// export const createTemplate = async (req, res) => {
//     const {coverImage, templateName, slug, listingType, category, region, genres, releaseDate, preOrder, dlc,price, specificCountrySellingOption, languages, videos, images, details } = req.body;
//     try {
//         // Check if a template with the same slug already exists
//         const existingTemplate = await Template.findOne({ slug });
//         if (existingTemplate) {
//             return res.status(400).json({ message: 'Template with this slug already exists' });
//         }

//         const generatedSlug = slug ? slug : slugify(templateName, { lower: true });
//         // Create a new template
//         const template = new Template({
//             coverImage,
//             templateName,
//             slug:generatedSlug,
//             listingType,
//             category,
//             region,
//             genres,
//             releaseDate,
//             preOrder,
//             dlc,
//             price,
//             specificCountrySellingOption,
//             languages,
//             videos,
//             images,
//             details,
//         });

//         // Save the template to the database
//         await template.save();
//         res.status(201).json({
//             message: 'Template created successfully',
//             data: template,
//         });
//     } catch (error) {
//         console.error(error);
//         res.status(500).json({ message: 'Server error', error: error.message });
//     }
// };
// // Get a template by ID
// export const getTemplateById = async (req, res) => {
//     try {
//         const template = await Template.findById(req.params.id).populate('category');

//         if (!template) {
//             return res.status(404).json({ message: 'Template not found' });
//         }

//         res.status(200).json(template);
//     } catch (error) {
//         console.error(error);
//         res.status(500).json({ message: 'Server error', error: error.message });
//     }
// };
// // Get all templates
// export const getAllTemplates = async (req, res) => {
//     try {
//         // Find all templates and populate the category field
//         const templates = await Template.find().populate('category');
//         res.status(200).json(templates);
//     } catch (error) {
//         console.error(error);
//         res.status(500).json({ message: 'Server error', error: error.message });
//     }
// };
// // Update a template by ID
// export const updateTemplate = async (req, res) => {
//     const {coverImage, templateName, slug, listingType, category, region, genres, releaseDate, preOrder, dlc, price,specificCountrySellingOption, languages, videos, images, details,active } = req.body;

//     try {
//         const template = await Template.findById(req.params.id);

//         if (!template) {
//             return res.status(404).json({ message: 'Template not found' });
//         }

//         // Update the template fields
//         template.coverImage = coverImage || template.coverImage;
//         template.price = price || template.price;
//         template.templateName = templateName || template.templateName;
//         template.slug = slug || template.slug;
//         template.listingType = listingType || template.listingType;
//         template.category = category || template.category;
//         template.region = region || template.region;
//         template.genres = genres || template.genres;
//         template.releaseDate = releaseDate || template.releaseDate;
//         template.preOrder = preOrder !== undefined ? preOrder : template.preOrder;
//         template.dlc = dlc || template.dlc;
//         template.specificCountrySellingOption = specificCountrySellingOption || template.specificCountrySellingOption;
//         template.languages = languages || template.languages;
//         template.videos = videos || template.videos;
//         template.images = images || template.images;
//         template.details = details || template.details;
//         template.active = active !== undefined ? active : template.active;

//         // Save the updated template
//         await template.save();

//         res.status(200).json({
//             message: 'Template updated successfully',
//             data: template,
//         });
//     } catch (error) {
//         console.error(error);
//         res.status(500).json({ message: 'Server error', error: error.message });
//     }
// };
// // Delete a template by ID
// export const deleteTemplate = async (req, res) => {
//     try {
//         const template = await Template.findById(req.params.id);

//         if (!template) {
//             return res.status(404).json({ message: 'Template not found' });
//         }

//         // Delete the template
//         await template.remove();

//         res.status(200).json({status:"success", message: 'Template deleted successfully' });
//     } catch (error) {
//         console.error(error);
//         res.status(500).json({ status:"success", error: error.message });
//     }
// };

// export const getOffer = factory.getOne(Offer, "template seller");
export const getTemplateById = factory.getOne(Template, "category subcategory");
export const createTemplate = factory.createOne(Template);
export const getAllTemplates = factory.getAll(
  "templates",
  Template,
  [{ path: "category" }, { path: "subcategory" }],
  "templateName"
);
export const updateTemplate = factory.updateOne(Template);
export const deleteTemplate = factory.deleteOne(Template);
