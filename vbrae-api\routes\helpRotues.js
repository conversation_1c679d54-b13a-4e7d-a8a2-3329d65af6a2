import express from "express";
import {
  getAllCategories,
  getCategory,
  addSubcategory,
  updateSubcategory,
  deleteSubcategory,
  addQuestion,
  updateQuestion,
  deleteQuestion,
    getSubCategory
} from "../controllers/helpController.js";

const router = express.Router();

// Get all categories
router.route("/").get(getAllCategories);
router.route("/:categoryId").get(getCategory);

// Subcategory routes
router.route("/:categoryId/subcategories").post(addSubcategory);
router
  .route("/:categoryId/subcategories/:subcategoryId")
  .get(getSubCategory)
  .patch(updateSubcategory)
  .delete(deleteSubcategory);

// Question routes
router
  .route("/:categoryId/subcategories/:subcategoryId/questions")
  .post(addQuestion);
router
  .route("/:categoryId/subcategories/:subcategoryId/questions/:questionId")
  .patch(updateQuestion)
  .delete(deleteQuestion);

export default router;
