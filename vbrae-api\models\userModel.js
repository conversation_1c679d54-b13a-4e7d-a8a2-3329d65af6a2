import mongoose from "mongoose";
import Order from "./OrderModel.js";
import Level from "./levelModel.js";
import Offer from "./offerModel.js";

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
    },

    email: {
      type: String,
      lowercase: true,
    },
    avatar: {
      type: String,
      default: "",
    },
    password: {
      type: String,
    },
    paymentId: {
      type: String,
      default: "",
    },
    isKycVerified: {
      type: String,
      enum: ['idle', 'approved', 'pending'],
      default: 'idle',
    },
    kycDetails: {
      verification_id: String,
      applicant_id: String,
      status: String,
      verified: Boolean,
      firstName: String,
      lastName: String,
      residenceCountry: String,
      dateOfBirth: Date,
      declineReasons: [String],
      lastVerificationDate: {
        type: Date,
        default: Date.now
      },
      documentVerified: Boolean,
      profileVerified: Boolean,
      verificationComment: String
    },
    address: {
      type: [
        {
          street: { type: String },
          city: { type: String },
          state: { type: String },
          zip: { type: String },
          country: { type: String },
        },
      ],
      default: [], // Default value set to an empty array
    },
    ipAddress: {
      type: String,
      required: true,
    },
    loginWithFacebook: {
      type: Boolean,
      default: false,
    },
    facebookID: {
      type: String,
    },
    currentOrder: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Order", // Reference to the latest order
      default: null,
    },
    loginWithGoogle: {
      type: Boolean,
      default: false,
    },
    country: {
      type: String,
      default: "",
    },
    phoneNumber: {
      type: String,
      default: "",
    },
    role: {
      type: String,
      enum: ["client", "seller", "admin"], // Define possible roles
      default: "client", // Default role is client
    },
    enable2FA: {
      type: Boolean,
      default: false,
    },
    lastActiveAt: {
      type: Date,
      default: Date.now
    },
    sellerStats: {
      totalOrders: { type: Number, default: 0 },
      completedOrders: { type: Number, default: 0 },
      averageRating: { type: Number, default: 0 },
      tier: { type: String, default: "New Seller" },
      totalSales: { type: Number, default: 0 },
      completionRate: { type: String, default: "0%" },
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt fields
  }
);

userSchema.methods.getSellerLevel = async function () {
  if (this.role !== "seller") {
    return null;
  }

  const completedOrders = await Order.countDocuments({
    user: this._id,
    status: { $in: ["completed"] },
  });
  // Find the matching level
    const [currentLevel, nextLevel] = await Promise.all([
        Level.findOne({
            fromOrders: { $lte: completedOrders },
            $or: [
                { toOrders: { $gte: completedOrders } },
                { toOrders: { $exists: false } },
                { toOrders: null },
            ],
        }).lean(),
        Level.findOne({
            fromOrders: { $gt: completedOrders },
        }).sort({ fromOrders: 1 }).lean(),
    ]);
  
  return {currentLevel, nextLevel, completedOrders};
};

userSchema.methods.getOfferCount = async function () {
    if (this.role !== "seller") {
        return null;
    }

    return Offer.countDocuments({
        seller: this._id,
    });
}

const User = mongoose.model("User", userSchema);

export default User;

//user id  databsae compare facebook --email  verify email
// google provide
//
