import express from "express";
import {
  createShopOpeningRequest,
  getAllShopOpeningRequests,
  getShopOpeningRequestById,
  updateShopOpeningRequest,
  deleteShopOpeningRequest, getMyShop,
} from "../controllers/shopOpeningRequestController.js";
import { protect, admin } from "../middleware/authMiddleware.js";

const router = express.Router();

// Public routes
router.route("/").get(getAllShopOpeningRequests);
router.route("/my-request").get(protect, getMyShop);
router.route("/:id").get(getShopOpeningRequestById);

// Protected routes (only authenticated users can create, only admin can update/delete)
router.use(protect);
router.route("/").post(createShopOpeningRequest);
router
  .route("/:id")
  .patch(admin, updateShopOpeningRequest)
  .delete(admin, deleteShopOpeningRequest);

export default router;
