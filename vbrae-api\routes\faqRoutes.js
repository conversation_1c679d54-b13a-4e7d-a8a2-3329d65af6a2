import express from "express";
import {
  createFAQ,
  getFAQ,
  getAllFAQs,
  updateFAQ,
  deleteFAQ,
} from "../controllers/faqController.js";
import { protect, admin } from "../middleware/authMiddleware.js";
const router = express.Router();
// Public routes
router.route("/").get(getAllFAQs);
router.route("/:id").get(getFAQ);
// Protected routes (admin only)
router.use(protect, admin);
router.route("/").post(createFAQ);
router.route("/:id").patch(updateFAQ).delete(deleteFAQ);

export default router;
