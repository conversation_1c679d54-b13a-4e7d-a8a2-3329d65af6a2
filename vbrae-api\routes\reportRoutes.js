import express from "express";
import { protect } from "../middleware/authMiddleware.js";
import {
  getUserReports,
  createReport,
  getReport,
  updateReport,
  deleteReport
} from "../controllers/reportController.js";

const router = express.Router();

// Protect all routes after this middleware
router.use(protect);

// Routes
router.route("/")
  .get(getUserReports)
  .post(createReport);

router.route("/:id")
  .get(getReport)
  .patch(updateReport)
  .delete(deleteReport);

export default router; 